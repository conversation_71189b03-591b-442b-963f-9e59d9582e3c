#include <algorithm>
#include <imgui.h>
#include <vector>

	static ImVec2 DWETL_Button_HSize = ImVec2(80.000000,80.000000);

//--ImVec2--

	static ImVec2 IMGUI_WindowSize = ImVec2(200, 200);



//--------------CacheVariable---------------------------------


//HInterpolation Tool
static float SimpleBezierInterpolation(float p0, float p3, float t)
{
	float u = 1.0f - t;
	float tt = t * t;
	float uu = u * u;
	float uuu = uu * u;
	float ttt = tt * t;

	float p = uuu * p0 + ttt * p3;
	return p;
}
static float LinearInterpolation(float a, float b, float t)
{
	return a + (b - a) * t;
}
struct H_InterpolationInfo
{
	size_t PreviousKey = 0;
	size_t LastOneKey = 0;
	float alpha = 0;
};
H_InterpolationInfo H_GetInterpolationInfoFromKeys(const std::vector<int> keys, int Frame)
{
	H_InterpolationInfo info;

	// Binary search finds the closest key
	auto upper = std::upper_bound(keys.begin(), keys.end(), Frame);
	info.LastOneKey = std::distance(keys.begin(), upper);
	info.PreviousKey = info.LastOneKey - 1;

	if (info.LastOneKey < keys.size() && info.PreviousKey >= 0) {
		int32_t max = keys[info.LastOneKey];
		int32_t min = keys[info.PreviousKey];

		if (max != min) {
			info.alpha = static_cast<float>(Frame - min) / (max - min);
		}
	}
	return info;
}


class HAnimationPlayer
{
public:
	void Play(void (*Animation)(int32_t Frame), size_t StartFram, size_t EndFrame, int FramePerSeconds, bool IsLoop)
	{
		Loop = IsLoop;
		PlayingAnimation = Animation;
		S_Fram = StartFram;
		E_Fram = EndFrame;
		FPS = FramePerSeconds;
		CurrentFrame = StartFram;
		DeltaTimeCalculateBuffer = 0;
	}

	void Stop()
	{
		PlayingAnimation = 0;
		Loop = false;
		DeltaTimeCalculateBuffer = 0;
		CurrentFrame = 0;
	}

	bool IsPlaying()
	{
		return PlayingAnimation;
	}

	//This function has been automatically called.
	void updata(float DeltaTime)
	{
		if (!PlayingAnimation)
			return;
		if (DeltaTimeCalculateBuffer >= 1.f / FPS)
		{
			DeltaTimeCalculateBuffer = 0;
			CurrentFrame++;
			if (CurrentFrame > E_Fram)
				if (Loop)
					CurrentFrame = S_Fram;
				else
					Stop();
			if(PlayingAnimation)
				PlayingAnimation(CurrentFrame);
		}
		else
		{
			DeltaTimeCalculateBuffer += DeltaTime;
		}
	}
private:
	void (*PlayingAnimation)(int32_t Frame);
	bool Loop;
	size_t S_Fram, E_Fram, CurrentFrame;
	int FPS;
	float DeltaTimeCalculateBuffer;
};
HAnimationPlayer AnimationPlayer;


//-------------------HAnimation------
// ImGuiWindow -Debug

#define NewSequencer_StartFrame 0
#define NewSequencer_EndFrame 64
#define NewSequencer_FramePerSeconds 30
void NewSequencer(int32_t Frame)
{

//---    Button
//HSize
    //printf("%f %f\n",DWETL_Button_HSize.x,DWETL_Button_HSize.y);
	const std::vector<int> DWETL_Button_HSize_keys = {  0 , 30 };
	const std::vector<ImVec2> DWETL_Button_HSize_values = {  ImVec2(0.000000,0.000000) , ImVec2(400.000000,100.000000) };
	H_InterpolationInfo DWETL_Button_HSize_info = H_GetInterpolationInfoFromKeys(DWETL_Button_HSize_keys , Frame );
	DWETL_Button_HSize.x = LinearInterpolation( DWETL_Button_HSize_values[DWETL_Button_HSize_info.PreviousKey].x , DWETL_Button_HSize_values[DWETL_Button_HSize_info.LastOneKey].x , DWETL_Button_HSize_info.alpha );
	DWETL_Button_HSize.y = LinearInterpolation( DWETL_Button_HSize_values[DWETL_Button_HSize_info.PreviousKey].y , DWETL_Button_HSize_values[DWETL_Button_HSize_info.LastOneKey].y , DWETL_Button_HSize_info.alpha );
}

// ImGuiWindow -Debug

#define NewSequencer_0_StartFrame 0
#define NewSequencer_0_EndFrame 64
#define NewSequencer_0_FramePerSeconds 30
void NewSequencer_0(int32_t Frame)
{
	const std::vector<int> BBZne_ColorButton_HPos_keys = {  0 , 5 };
	const std::vector<ImVec2> BBZne_ColorButton_HPos_values = {  ImVec2(200, 200) , ImVec2(1080, 720) };
	H_InterpolationInfo BBZne_ColorButton_HPos_info = H_GetInterpolationInfoFromKeys(BBZne_ColorButton_HPos_keys , Frame );
	IMGUI_WindowSize.x = LinearInterpolation( BBZne_ColorButton_HPos_values[BBZne_ColorButton_HPos_info.PreviousKey].x , BBZne_ColorButton_HPos_values[BBZne_ColorButton_HPos_info.LastOneKey].x , BBZne_ColorButton_HPos_info.alpha );
	IMGUI_WindowSize.y = LinearInterpolation( BBZne_ColorButton_HPos_values[BBZne_ColorButton_HPos_info.PreviousKey].y , BBZne_ColorButton_HPos_values[BBZne_ColorButton_HPos_info.LastOneKey].y , BBZne_ColorButton_HPos_info.alpha );
}

void NewSequencer_1(int32_t Frame)
{
	const std::vector<int> BBZne_ColorButton_HPos_keys = {  0 , 5 };
	const std::vector<ImVec2> BBZne_ColorButton_HPos_values = {  ImVec2(1080, 720) , ImVec2(200, 200) };
	H_InterpolationInfo BBZne_ColorButton_HPos_info = H_GetInterpolationInfoFromKeys(BBZne_ColorButton_HPos_keys , Frame );
	IMGUI_WindowSize.x = LinearInterpolation( BBZne_ColorButton_HPos_values[BBZne_ColorButton_HPos_info.PreviousKey].x , BBZne_ColorButton_HPos_values[BBZne_ColorButton_HPos_info.LastOneKey].x , BBZne_ColorButton_HPos_info.alpha );
	IMGUI_WindowSize.y = LinearInterpolation( BBZne_ColorButton_HPos_values[BBZne_ColorButton_HPos_info.PreviousKey].y , BBZne_ColorButton_HPos_values[BBZne_ColorButton_HPos_info.LastOneKey].y , BBZne_ColorButton_HPos_info.alpha );
}