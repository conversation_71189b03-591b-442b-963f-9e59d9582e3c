#pragma once
#include <fstream>
#include <fcntl.h>
#include <iostream>
#include <unistd.h>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/sysmacros.h>
#include "qx.hpp"
using namespace std;
typedef unsigned short UTF16;
#if defined(__aarch64__) && defined(__ANDROID__)
#include "Log.h"
#include "VecTool.h"
#endif

struct Transform {
    VecTor3 Scale3D;
    VecTor4 Rotation;
   	VecTor3 Translation;
};

struct FMatrix {
    float M[4][4];
};

class ImGuiTOOL {
	public:
	pid_t Pid = -1;
	int FileDescriPtion = -1;
	float Matrix[4][4] = {0};
	UTF16 BUFFER16[16] = {0};
	Transform MeshTrans, MeshTran;
	uintptr_t ModulesBase[10] = {0};
	
	TOUCH_INFORMATION touch_information;
	FMatrix XMatrix, BoneMatrix, OutcMatrix;
	uintptr_t MeshAddress = 0, BoneAddress = 0;
	RESOLUTION_INFORMATION resolution_information;
	IMGUISWITCH_INFORMATION imguiswitch_information;
	
	int ContainsDigit(char *str);
	int FindAndCreateDeviceNode();
	int GetMainDeviceNumber(char *path);
	
	int readcount(int *c, int num) {
        ++*c;
        return num;
    }
	void initialize() {
        driver1->initialize(Pid);
    }
	bool read(uintptr_t address, void *buffer, size_t size) {
		initialize();
		return driver1->read(address, buffer, size);
	}
	
	bool write(uintptr_t address, void *buffer, size_t size) {
		return driver1->write(address, buffer, size);
	}
	
	template <typename start>
    start read(uintptr_t address) {
        start buffer;
        if (read(address, &buffer, sizeof(start))) {
            return buffer;
		}
        return {};
    }
	
	template<typename start>
    bool read(uintptr_t address, start *buffer) {
      	return read(address, buffer, sizeof(start));
    }
	
	template<typename... s>
    uintptr_t GetPointer(uintptr_t address, s... args) {
        int count = 0;
		uintptr_t last_address = 0;
        int array[] = {(readcount(&count, args))...};
        read(address + array[0], &last_address);
        for (int i = 1; i < count; i++) {
            if (i == count - 1) {
                last_address += array[i];
                return last_address;
            }
            read(last_address + array[i], &last_address);
        }
        return last_address;
    }
	
	void GetPid(const char* name) {
		char buffer[0x100] = "pidof ";
		strcat(buffer, name);
		FILE* file = popen(buffer, "r");
		if (file) {
			fscanf(file, "%d", &Pid);
			initialize();
		}
		pclose(file);
    }
	
	uintptr_t GetModuleAddressOne(const char* name) {
		char* pch;
		char line[1024] = {0};
		uintptr_t address = 0;
		char filename[64] = {0};
		snprintf(filename, sizeof(filename), "/proc/%d/maps", Pid);
		FILE* file = fopen(filename, "r");
		if (file != nullptr) {
			while (fgets(line, sizeof(line), file)) {
				if (strstr(line, name)) {
					pch = strtok(line, "-");
					address = strtoul(pch, NULL, 16);
					if (address == 0x8000) {
						address = 0;
					}
					break;
				}
			}
			fclose(file);
		}
		return address;
	}
	
	uintptr_t GetModuleAddressTwo(char* name) {
		return driver1->get_module_base(name);
	}
	
	string exec(const char* str) {
        string result = "";
		char buffer[1024] = "";
        FILE* PIPE = popen(str, "r");
        if (!PIPE) {
            return "";
        }
        while (!feof(PIPE)) {
            if (fgets(buffer, sizeof(buffer), PIPE) != nullptr) {
                result += buffer;
            }
        }
        pclose(PIPE);
        return result;
    }
	int has_upper = 0;
int has_lower = 0;
int has_symbol = 0;
int has_digit = 0;
    int symbol_file(const char *filename)
    {
        int length = strlen(filename);

        for (int i = 0; i < length; i++)
        {
            if (isupper(filename[i]))
            {
                has_upper = 1;
            }
            else if (islower(filename[i]))
            {
                has_lower = 1;
            }
            else if (ispunct(filename[i]))
            {
                has_symbol = 1;
            }
            else if (isdigit(filename[i]))
            {
                has_digit = 1;
            }
        }

        return has_upper && has_lower && !has_symbol && !has_digit;
    }

    char *execCom(const char *shell)
    {
        FILE *fp = popen(shell, "r");

        if (fp == NULL)
        {
            perror("popen failed");
            return NULL;
        }

        char buffer[256];
        char *result = (char *)malloc(1000); // allocate memory for the result string
        result[0] = '\0';                    // initialize as an empty string

        // Read and append output of the first command to result
        while (fgets(buffer, sizeof(buffer), fp) != NULL)
        {
            strcat(result, buffer);
        }
        pclose(fp);
        return result;
    }

    int findFirstMatchingPath(const char *path, regex_t *regex, char *result)
    {
        DIR *dir;
        struct dirent *entry;

        if ((dir = opendir(path)) != NULL)
        {
            while ((entry = readdir(dir)) != NULL)
            {
                char fullpath[1024]; // 适当调整数组大小
                snprintf(fullpath, sizeof(fullpath), "%s/%s", path, entry->d_name);
                if (entry->d_type == DT_LNK)
                {
                    // 对链接文件进行处理
                    char linkpath[1024]; // 适当调整数组大小
                    ssize_t len = readlink(fullpath, linkpath, sizeof(linkpath) - 1);
                    if (len != -1)
                    {
                        linkpath[len] = '\0';
                        // printf("%s\n", linkpath);
                        // 对链接的实际路径进行正则匹配
                        if (regexec(regex, linkpath, 0, NULL, 0) == 0)
                        {
                            strcpy(result, fullpath);
                            closedir(dir);
                            return 1;
                        }
                    }
                    else
                    {
                        perror("readlink");
                    }
                }
            }
            closedir(dir);
        }
        else
        {
            perror("Unable to open directory");
        }

        return 0;
    }

    // 修复createDriverNode和removeDeviceNode函数
    void createDriverNode(char *path, int major_number, int minor_number)
    {
        std::string command = "mknod " + std::string(path) + " c " + std::to_string(major_number) + " " + std::to_string(minor_number);
        system(command.c_str());
    }

    // 删除驱动节点
    // 新的函数，用于删除设备节点
    void removeDeviceNode(char *path)
    {
        // printf("%s\n",path);
        if (unlink(path) == 0)
        {
        //    printf("[-] 驱动安全守护已激活\n");
            // cerr << "已删除设备节点：" << devicePath << endl;
        }
        else
        {
        //    printf("[-] 驱动安全守护执行错误\n");
            // perror("删除设备节点时发生错误");
        }
    }

char* driver_path()
{
   const char *dev_path = "/dev";
		DIR *dir = opendir(dev_path);
		if (dir == NULL){
			printf("无法打开/dev目录\n");
			return NULL;
		}

		char *files[] = { "wanbai", "CheckMe", "Ckanri", "lanran","video188"};
		struct dirent *entry;
		char *file_path = NULL;
		while ((entry = readdir(dir)) != NULL) {
			// 跳过当前目录和上级目录
			if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
				continue;
			}

			size_t path_length = strlen(dev_path) + strlen(entry->d_name) + 2;
			file_path = (char *)malloc(path_length);
			snprintf(file_path, path_length, "%s/%s", dev_path, entry->d_name);
			for (int i = 0; i < 5; i++) {
				if (strcmp(entry->d_name, files[i]) == 0) {
					//printf("驱动文件：%s\n", file_path);
					closedir(dir);
					return file_path;
				}
			}

			// 获取文件stat结构
			struct stat file_info;
			if (stat(file_path, &file_info) < 0) {
				free(file_path);
				file_path = NULL;
				continue;
			}

			// 跳过gpio接口
			if (strstr(entry->d_name, "gpiochip") != NULL) {
				free(file_path);
				file_path = NULL;
				continue;
			}
            if (strchr(entry->d_name, '0') != NULL || strchr(entry->d_name, '1') != NULL || strchr(entry->d_name, '2') != NULL || strchr(entry->d_name, '3') != NULL || strchr(entry->d_name, '4') != NULL || strchr(entry->d_name, '5') != NULL || strchr(entry->d_name, '6') != NULL || strchr(entry->d_name, '7') != NULL || strchr(entry->d_name, '8') != NULL || strchr(entry->d_name, '9') != NULL) {
    free(file_path);
    file_path = NULL;
    continue;
}

			// 检查是否为驱动文件
			if ((S_ISCHR(file_info.st_mode) || S_ISBLK(file_info.st_mode))&& strchr(entry->d_name, '_') == NULL && strchr(entry->d_name, '-') == NULL && strchr(entry->d_name, ':') == NULL) {
				// 过滤标准输入输出
				if (strcmp(entry->d_name, "stdin") == 0 || strcmp(entry->d_name, "stdout") == 0 || strcmp(entry->d_name, "stderr") == 0) {
					free(file_path);
					file_path = NULL;
					continue;
				}
				
				size_t file_name_length = strlen(entry->d_name);
				time_t current_time;
				time(&current_time);
				int current_year = localtime(&current_time)->tm_year + 1900;
				int file_year = localtime(&file_info.st_ctime)->tm_year + 1900;
				//跳过1980年前的文件
				if (file_year <= 1980) {
					free(file_path);
					file_path = NULL;
					continue;
				}
				
				time_t atime = file_info.st_atime;
				time_t ctime = file_info.st_ctime;
				// 检查最近访问时间和修改时间是否一致并且文件名是否是symbol文件
				if ((atime == ctime)) {
					//检查mode权限类型是否为S_IFREG(普通文件)和大小还有gid和uid是否为0(root)并且文件名称长度在7位或7位以下
					if ((file_info.st_mode & S_IFMT) == 8192 && file_info.st_size == 0
						&& file_info.st_gid == 0 && file_info.st_uid == 0 && file_name_length <= 7) {
						//printf("驱动文件：%s\n", file_path);
						closedir(dir);
						return file_path;
					}
				}
			}
			free(file_path);
			file_path = NULL;
		}
		closedir(dir);
		return NULL;
}



  int get_dev() {
    char *output = execCom("ls -l /proc/*/exe 2>/dev/null | grep -E \"/data/[a-z]{6} \\(deleted\\)\"");
        char filePath[256];
        char pid[56];
        if (output != NULL)
        {
            char *procStart = strstr(output, "/proc/");

            // Extracting process ID
            char *pidStart = procStart + 6; // Move to the position after "/proc/"
            char *pidEnd = strchr(pidStart, '/');

            strncpy(pid, pidStart, pidEnd - pidStart);
            pid[pidEnd - pidStart] = '\0';

            char *arrowStart = strstr(output, "->");
            // Extracting file path
            char *start = arrowStart + 3;        // Move to the position after "->"
            char *end = strchr(output, '(') - 1; // Find the position before '('
            strncpy(filePath, start, end - start + 1);
            filePath[end - start] = '\0';

            // Replace "data" with "dev" in filePath
            char *replacePtr = strstr(filePath, "data");
            if (replacePtr != NULL)
            {
                memmove(replacePtr + 2, replacePtr + 3, strlen(replacePtr + 3) + 1);
                memmove(replacePtr, "dev", strlen("dev"));
            }
            // Print the results
            // printf("Driver Path: %s\n", filePath);
        }
        else
        {
            printf("Error executing scripts.\n");
        }
        char fdPath[256]; // fd路径
        // const char *pattern = "^/dev/.*\\s*\\(deleted\\)";  // ^/dev/[a-z]{6}\\s*\\(deleted\\) 不清楚这样为啥有问题....
        char pattern[100];
        snprintf(pattern, sizeof(pattern), ".*%s.*", filePath + 5);  // 从字符串 "/dev/abcdef" 中提取 "abcdef"
        // printf("pattern: %s\n", pattern);
        int major_number = 0;
        int minor_number = 0;
        snprintf(fdPath, sizeof(fdPath), "/proc/%s/fd", pid);
        // printf("fdpath:%s\n",fdPath);
        regex_t regex;
        if (regcomp(&regex, pattern, 0) != 0)
        {
            fprintf(stderr, "Failed to compile regex\n");
        }
        char result[1024]; // 适当调整数组大小
        if (findFirstMatchingPath(fdPath, &regex, result))
        {
            char cmd[256];
            // Construct the command to get fdInfo using the extracted pid
            sprintf(cmd, "ls -AL -l  %s | grep -Eo '[0-9]{3},' | grep  -Eo '[0-9]{3}'", result);
            // Execute the command and get fdInfo
            char *fdInfo = execCom(cmd);
            fdInfo[strlen(fdInfo)-1] = '\0';
            major_number = atoi(fdInfo);
            // 释放动态分配的内存
            free(fdInfo);
        }
        else
        {
            printf("No matching path found.\n");
        }
        regfree(&regex);
    
        if (filePath != "\0")
        {
            // std::cout << "创建 /dev/" << driverInfo.deviceID << std::endl;
            createDriverNode(filePath, major_number, 0);
            FileDescriPtion = open(filePath, O_RDWR); // Use c_str() to get a C-style string
            // printf("%d",fd);
            if (FileDescriPtion == -1)
            {

             //   printf("\n[-] 驱动链接失败\n");
                removeDeviceNode(filePath);
                return -1;
            }
            else
            {
             //   printf("\n[-] 驱动已经启动\n");
                removeDeviceNode(filePath);
                return 1;
            }
        }
        return -1;
    }
	ImGuiTOOL() {
		/*char *search = driver_path();
        fd = open(search, O_RDWR);
        if (fd == -1) {
        printf("%s", "\n[-]内核读取成功\n");
		  exit(0);
        }
        if (fd != -1){
        printf("%s", "\n[+]Alan独家驱动启动成功\n");
        }*/
		/*FileDescriPtion = driver_path();
		if (FileDescriPtion <= 0) {
        //	cout << "\033[31;1m[-] 载入驱动失败!\033[30;1m" << endl;
			exit(EXIT_SUCCESS);
    	} else {
		//	cout << "\033[32;1m[+] 载入驱动成功!\033[30;1m" << endl;
		}*/
		memset(&touch_information, 0, sizeof(TOUCH_INFORMATION));
		memset(&resolution_information, 0, sizeof(RESOLUTION_INFORMATION));
		memset(&imguiswitch_information, 0, sizeof(IMGUISWITCH_INFORMATION));
	}
	
	~ImGuiTOOL() {
if (FileDescriPtion > 0) {
			close(FileDescriPtion);
		}
	}
};

