#include "imgui.h"
#include "ImGuiStructure.h"

float tm1 = 127/255.f;
ImVec4 arr[] = {{144/255.f,238/255.f,144/255.f,tm1},{135/255.f,206/255.f,255/255.f,tm1},{255/255.f,0/255.f,0/255.f,tm1},{0/255.f,255/255.f,0/255.f,tm1},{0 /255.f,255/255.f, 127/255.f,tm1}
                      ,{255/255.f,182/255.f,193/255.f,tm1},{218/255.f,112/255.f,214/255.f,tm1},{248/255.f,248/255.f,255/255.f,tm1},{0/255.f,255/255.f,255/255.f,tm1},{255/255.f,165/255.f,0/255.f,tm1}
                      ,{153/255.f,204/255.f,255/255.f,tm1},{204/255.f,255/255.f,153/255.f,tm1},{255/255.f,255/255.f,153/255.f,tm1},{255/255.f,153/255.f,153/255.f,tm1},{153/255.f,153/255.f,204/255.f,tm1}
                      ,{204/255.f,204/255.f,204/255.f,tm1},{102/255.f,204/255.f,153/255.f,tm1},{255/255.f,102/255.f,0/255.f,tm1},{102/255.f,204/255.f,204/255.f,tm1},{153/255.f,204/255.f,255/255.f,tm1}
};
int length = sizeof(arr) / 16;



ImColor RandomColor() {
    int R = 0;
	int G = 0;
	int B = 0;
	int A = 180;
    R = (random() % 255);
    G = (random() % 255);
    B = (random() % 255);
    return ImColor(R, G, B, A);
}

ImColor ArrayColor[100];
void InitializeColor() {
    for (int Count = 0; Count < 100; Count++) {
        ArrayColor[Count] = RandomColor();
    }
}

ImColor TeamColor(int Count) {
    if (Count < 100 && Count > 0) {
        return ArrayColor[Count];
    } else {
        return ImColor(100, 255, 100, 180);
    }
}




// 定义枚举类型来表示不同的血条类型
enum HealthBarType {
    CircleArc,
    RectangleFilled,
    CustomRectangle
};

void skeleton(ImDrawList* drawlist, const ImVec2& start, const ImVec2& end, ImColor color, float thickness) {
    drawlist->AddLine(start, end, color, thickness);
}//骨骼

void DrawLineIfCondition(ImDrawList* drawlist, ImVec2 start, ImVec2 end, ImU32 color, float thickness, bool condition) {
    if (condition) {
        drawlist->AddLine(start, end, color, thickness);
    }
}//射线人机

void DrawLines(ImDrawList* drawlist, RESOLUTION_INFORMATION resolution_information, SHARED_INFORMATION* shared_information, int objectCount, IMGUISWITCH_INFORMATION imguiswitch_information) {
    bool isBootCondition = shared_information[objectCount].IsBoot == 1;
    ImVec2 start = {resolution_information.Width, 0};
    ImVec2 end = {shared_information[objectCount].ScreenCoordinates.x, shared_information[objectCount].HeadSkeletonCoordinates - 60};
    float thickness = imguiswitch_information.floatswitch[57];
    
    
    DrawLineIfCondition(drawlist, start, end, ImColor(255, 255, 255, 255), thickness, isBootCondition);
    
    
    drawlist->AddLine(start, end, imguiswitch_information.colorswitch[9], thickness);
}//射线真人




// 绘制血条
void DrawHealthBar(ImDrawList* drawlist, SHARED_INFORMATION* shared_information, HealthBarType healthBarType, const IMGUISWITCH_INFORMATION imguiswitch_information, int objectCount) {
    switch (healthBarType) {
        case CircleArc: {
            ImColor healthColor;
            ImVec4 hpColor = shared_information[objectCount].Health < 80 ? 
                             shared_information[objectCount].Health < 60 ? 
                             shared_information[objectCount].Health < 30 ? 
                             ImVec4{ 0.5f,0.0f,0.0f,127/255.f } : 
                             ImVec4{ 1, 0, 0, 127/255.f } : 
                             ImVec4{ 1, 1, 0, 127/255.f } : 
                             ImVec4{ 255/255.f, 255/255.f, 255/255.f, 127/255.f };
                             
            if (shared_information[objectCount].Health >= 80) {
                healthColor = ImColor(10, 240, 10, 210);
            } else {
                healthColor = ImColor(hpColor);
            }

            float aa = shared_information[objectCount].Health * 3.6f;
            drawlist->AddCircleArc(
                {shared_information[objectCount].ScreenCoordinates.x - 35, shared_information[objectCount].ScreenCoordinates.y + 25},
                10,
                {0, aa},
                healthColor,
                0,
                5
            );
            break;
        }
        case RectangleFilled: {
            drawlist->AddRectFilled(
                {shared_information[objectCount].ScreenCoordinates.x - 75, shared_information[objectCount].HeadSkeletonCoordinates - 30},
                {shared_information[objectCount].ScreenCoordinates.x - 75 + (1.5f * shared_information[objectCount].Health), shared_information[objectCount].HeadSkeletonCoordinates - 10},
                TeamColor(shared_information[objectCount].Team),
                30
            );
            drawlist->AddRect(
                {shared_information[objectCount].ScreenCoordinates.x - 75, shared_information[objectCount].HeadSkeletonCoordinates - 30},
                {shared_information[objectCount].ScreenCoordinates.x + 75, shared_information[objectCount].HeadSkeletonCoordinates - 10},
                ImColor(0, 0, 0, 200),
                30,
                0,
                1.5f
            );
            break;
        }
        case CustomRectangle: {
            if (imguiswitch_information.boolswitch[6]) {
                const float heightReduction = 12.0f;
                const float yOffset = 15.0f;

                float totalBorderWidth = shared_information[objectCount].ScreenCoordinates.x + 60 - (shared_information[objectCount].ScreenCoordinates.x - 60);
                const int segments = 5;
                const float segmentWidth = totalBorderWidth / segments;

                drawlist->AddRectFilled(
                    ImVec2(shared_information[objectCount].ScreenCoordinates.x - 60,
                           shared_information[objectCount].HeadSkeletonCoordinates - 30 + heightReduction / 2 + yOffset),
                    ImVec2(shared_information[objectCount].ScreenCoordinates.x - 60 + (1.2f * shared_information[objectCount].Health),
                           shared_information[objectCount].HeadSkeletonCoordinates - 10 - heightReduction / 2 + yOffset),
                    ImColor(255, 192, 203)
                );

                drawlist->AddRect(
                    ImVec2(shared_information[objectCount].ScreenCoordinates.x - 60,
                           shared_information[objectCount].HeadSkeletonCoordinates - 30 + heightReduction / 2 + yOffset),
                    ImVec2(shared_information[objectCount].ScreenCoordinates.x + 60,
                           shared_information[objectCount].HeadSkeletonCoordinates - 10 - heightReduction / 2 + yOffset),
                    ImColor(0, 0, 0, 200)
                );

                for (int i = 0; i < segments; ++i) {
                    float xPosition = shared_information[objectCount].ScreenCoordinates.x - 60 + (i * segmentWidth);
                    drawlist->AddLine(
                        ImVec2(xPosition, shared_information[objectCount].HeadSkeletonCoordinates - 30 + heightReduction / 2 + yOffset),
                        ImVec2(xPosition, shared_information[objectCount].HeadSkeletonCoordinates - 10 - heightReduction / 2 + yOffset),
                        ImColor(0, 0, 0, 255)
                    );
                }
            }
            break;
        }
    }
}







//灵动岛
float animationProgress = 0.0f; // 动画进度，0.0表示收缩状态，1.0表示展开状态
bool isExpanding = false;
float animationSpeed = 0.02f; // 动画速度

void drawDynamicIsland(ImDrawList* drawlist, int aimitar, RESOLUTION_INFORMATION& resolution_information, SHARED_INFORMATION* shared_information, int 真人数量, int 人机数量) {
    // 更新展开状态
    isExpanding = (aimitar != -1);

    // 更新动画进度
    if (isExpanding) {
        animationProgress = std::min(animationProgress + animationSpeed, 1.0f);
    } else {
        animationProgress = std::max(animationProgress - animationSpeed, 0.0f);
    }

    // 计算UI组件的大小和位置
    float widthOffset = 80 + 100 * animationProgress;
    float alpha = 255 * animationProgress;

    // 绘制展开矩形（根据动画进度）
    if (animationProgress > 0) {
        drawlist->AddRectFilled(
            ImVec2(resolution_information.Width - widthOffset, 40),
            ImVec2(resolution_information.Width + widthOffset, 110),
            ImColor(255, 255, 255, (int)alpha), 50
        );

        // 绘制距离和血量信息（仅当aimitar != -1时）
        if (aimitar != -1) {
            drawlist->AddText(
                NULL, 42,
                ImVec2(resolution_information.Width - widthOffset + 20, 55),
                ImColor(0, 0, 255, (int)alpha),
                std::to_string((int)shared_information[aimitar].Distance).c_str()
            );

            drawlist->AddText(
                NULL, 42,
                ImVec2(resolution_information.Width + widthOffset - 70, 55),
                ImColor(0, 0, 255, (int)alpha),
                std::to_string((int)shared_information[aimitar].Health).c_str()
            );
        }
    }

    // 始终绘制固定矩形，显示真人数量和人机数量
    drawlist->AddRectFilled(
        ImVec2(resolution_information.Width - 80, 40),
        ImVec2(resolution_information.Width + 80, 110),
        ImColor(255, 255, 255, 255), 50
    );

    drawlist->AddText(
        NULL, 42,
        ImVec2(resolution_information.Width - 50, 55),
        ImColor(255, 0, 0, 255),
        std::to_string(真人数量).c_str()
    );

    drawlist->AddText(
        NULL, 42,
        ImVec2(resolution_information.Width + 30, 55),
        ImColor(0, 255, 0, 255),
        std::to_string(人机数量).c_str()
    );
}