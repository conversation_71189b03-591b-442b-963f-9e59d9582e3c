//获取进程
#pragma once
#include <sys/fcntl.h>
#include <sys/ioctl.h>
#include <stdio.h>
#include <stdlib.h>
#include <dirent.h>
#include <sys/stat.h>
#include <string.h>
#include <time.h>
#include<ctype.h>
#include<regex.h>
#include<string>

class c_driver {
private:
int has_upper = 0;
int has_lower = 0;
int has_symbol = 0;
int has_digit = 0;

    int fd;
    pid_t pid;

    typedef struct _COPY_MEMORY {
        pid_t pid;
        uintptr_t addr;
        void* buffer;
        size_t size;
    } COPY_MEMORY, *PCOPY_MEMORY;

    typedef struct _MODULE_BASE {
        pid_t pid;
        char* name;
        uintptr_t base;
    } MODULE_BASE, *PMODULE_BASE;

    enum OPERATIONS {
        OP_INIT_KEY = 0x800,
        OP_READ_MEM = 0x801,
        OP_WRITE_MEM = 0x802,
        OP_MODULE_BASE = 0x803,
    };


    int symbol_file(const char *filename)
    {
        int length = strlen(filename);

        for (int i = 0; i < length; i++)
        {
            if (isupper(filename[i]))
            {
                has_upper = 1;
            }
            else if (islower(filename[i]))
            {
                has_lower = 1;
            }
            else if (ispunct(filename[i]))
            {
                has_symbol = 1;
            }
            else if (isdigit(filename[i]))
            {
                has_digit = 1;
            }
        }

        return has_upper && has_lower && !has_symbol && !has_digit;
    }

    char *execCom(const char *shell)
    {
        FILE *fp = popen(shell, "r");

        if (fp == NULL)
        {
            perror("popen failed");
            return NULL;
        }

        char buffer[256];
        char *result = (char *)malloc(1000); // allocate memory for the result string
        result[0] = '\0';                    // initialize as an empty string

        // Read and append output of the first command to result
        while (fgets(buffer, sizeof(buffer), fp) != NULL)
        {
            strcat(result, buffer);
        }
        pclose(fp);
        return result;
    }

    int findFirstMatchingPath(const char *path, regex_t *regex, char *result)
    {
        DIR *dir;
        struct dirent *entry;

        if ((dir = opendir(path)) != NULL)
        {
            while ((entry = readdir(dir)) != NULL)
            {
                char fullpath[1024]; // 适当调整数组大小
                snprintf(fullpath, sizeof(fullpath), "%s/%s", path, entry->d_name);
                if (entry->d_type == DT_LNK)
                {
                    // 对链接文件进行处理
                    char linkpath[1024]; // 适当调整数组大小
                    ssize_t len = readlink(fullpath, linkpath, sizeof(linkpath) - 1);
                    if (len != -1)
                    {
                        linkpath[len] = '\0';
                        // printf("%s\n", linkpath);
                        // 对链接的实际路径进行正则匹配
                        if (regexec(regex, linkpath, 0, NULL, 0) == 0)
                        {
                            strcpy(result, fullpath);
                            closedir(dir);
                            return 1;
                        }
                    }
                    else
                    {
                        perror("readlink");
                    }
                }
            }
            closedir(dir);
        }
        else
        {
            perror("Unable to open directory");
        }

        return 0;
    }

    // 修复createDriverNode和removeDeviceNode函数
    void createDriverNode(char *path, int major_number, int minor_number)
    {
        std::string command = "mknod " + std::string(path) + " c " + std::to_string(major_number) + " " + std::to_string(minor_number);
        system(command.c_str());
    }

    // 删除驱动节点
    // 新的函数，用于删除设备节点
    void removeDeviceNode(char *path)
    {
        // printf("%s\n",path);
        if (unlink(path) == 0)
        {
        //    printf("[-] 驱动安全守护已激活\n");
            // cerr << "已删除设备节点：" << devicePath << endl;
        }
        else
        {
        //    printf("[-] 驱动安全守护执行错误\n");
            // perror("删除设备节点时发生错误");
        }
    }




int get_dev() {
    char *output = execCom("ls -l /proc/*/exe 2>/dev/null | grep -E \"/data/[a-z]{6} \\(deleted\\)\"");
        char filePath[256];
        char pid[56];
        if (output != NULL)
        {
            char *procStart = strstr(output, "/proc/");

            // Extracting process ID
            char *pidStart = procStart + 6; // Move to the position after "/proc/"
            char *pidEnd = strchr(pidStart, '/');

            strncpy(pid, pidStart, pidEnd - pidStart);
            pid[pidEnd - pidStart] = '\0';

            char *arrowStart = strstr(output, "->");
            // Extracting file path
            char *start = arrowStart + 3;        // Move to the position after "->"
            char *end = strchr(output, '(') - 1; // Find the position before '('
            strncpy(filePath, start, end - start + 1);
            filePath[end - start] = '\0';

            // Replace "data" with "dev" in filePath
            char *replacePtr = strstr(filePath, "data");
            if (replacePtr != NULL)
            {
                memmove(replacePtr + 2, replacePtr + 3, strlen(replacePtr + 3) + 1);
                memmove(replacePtr, "dev", strlen("dev"));
            }
            // Print the results
            // printf("Driver Path: %s\n", filePath);
        }
        else
        {
            printf("Error executing scripts.\n");
        }
        char fdPath[256]; // fd路径
        // const char *pattern = "^/dev/.*\\s*\\(deleted\\)";  // ^/dev/[a-z]{6}\\s*\\(deleted\\) 不清楚这样为啥有问题....
        char pattern[100];
        snprintf(pattern, sizeof(pattern), ".*%s.*", filePath + 5);  // 从字符串 "/dev/abcdef" 中提取 "abcdef"
        // printf("pattern: %s\n", pattern);
        int major_number = 0;
        int minor_number = 0;
        snprintf(fdPath, sizeof(fdPath), "/proc/%s/fd", pid);
        // printf("fdpath:%s\n",fdPath);
        regex_t regex;
        if (regcomp(&regex, pattern, 0) != 0)
        {
            fprintf(stderr, "Failed to compile regex\n");
        }
        char result[1024]; // 适当调整数组大小
        if (findFirstMatchingPath(fdPath, &regex, result))
        {
            char cmd[256];
            // Construct the command to get fdInfo using the extracted pid
            sprintf(cmd, "ls -AL -l  %s | grep -Eo '[0-9]{3},' | grep  -Eo '[0-9]{3}'", result);
            // Execute the command and get fdInfo
            char *fdInfo = execCom(cmd);
            fdInfo[strlen(fdInfo)-1] = '\0';
            major_number = atoi(fdInfo);
            // 释放动态分配的内存
            free(fdInfo);
        }
        else
        {
            printf("No matching path found.\n");
        }
        regfree(&regex);
    
        if (filePath != "\0")
        {
            // std::cout << "创建 /dev/" << driverInfo.deviceID << std::endl;
            createDriverNode(filePath, major_number, 0);
            fd = open(filePath, O_RDWR); // Use c_str() to get a C-style string
            // printf("%d",fd);
            if (fd == -1)
            {

             //   printf("\n[-] 驱动链接失败\n");
                removeDeviceNode(filePath);
                return -1;
            }
            else
            {
             //   printf("\n[-] 驱动已经启动\n");
                removeDeviceNode(filePath);
                return 1;
            }
        }
        return -1;
    }
    
    

public:
    c_driver() {
        
         get_dev();
		
       
    }

    ~c_driver() {
        //wont be called
        if (fd > 0)
            close(fd);
    }

    void initialize(pid_t pid) {
        this->pid = pid;
    }

    bool init_key(char* key) {
        char buf[0x100];
        strcpy(buf,key);
        if (ioctl(fd, OP_INIT_KEY, buf) != 0) {
            return false;
        }
        return true;
    }

    bool read(uintptr_t addr, void *buffer, size_t size) {
        COPY_MEMORY cm;

        cm.pid = this->pid;
        cm.addr = addr&0xFFFFFFFFFF;
        cm.buffer = buffer;
        cm.size = size;

        if (ioctl(fd, OP_READ_MEM, &cm) != 0) {
            return false;
        }
        return true;
    }

    bool write(uintptr_t addr, void *buffer, size_t size) {
        COPY_MEMORY cm;

        cm.pid = this->pid;
        cm.addr = addr&0xFFFFFFFFFF;
        cm.buffer = buffer;
        cm.size = size;

        if (ioctl(fd, OP_WRITE_MEM, &cm) != 0) {
            return false;
        }
        return true;
    }
    
    template <typename T>
    bool write(uintptr_t addr,T value) {
        return this->write(addr, &value, sizeof(T));
    }
    uintptr_t get_module_base(char* name) {
        MODULE_BASE mb;
        char buf[0x100];
        strcpy(buf,name);
        mb.pid = this->pid;
        mb.name = buf;

        if (ioctl(fd, OP_MODULE_BASE, &mb) != 0) {
            return 0;
        }
        return mb.base;
    }
};

static c_driver *driver1 = new c_driver();

// 读取字符信息
