#include "ImGuiELGS.h"
#include "font_1.h"
#include "font_2.h"

// c_driver *driver1;

float dx = 20.f, yuan, zuo, you;
float ycsz, ycsd, ycsp;
float xyd;
bool show556, show762, showRif<PERSON>, showSubmachine, showSniper, showMirror, showExpansion, showOtherParts, showDrug, showArmor, 空投, 骨灰盒, 地铁;
bool 透明 = false, 观透 = false, 首次 = false;
bool aigl = false, dynamic;
bool 类名 = false, 无敌炫酷;
int 血条 = 0;
int ArraysCount;
int OwnTeam = 0;
uintptr_t 基址头;
struct 当前手持 hand_hold;
pid_t pid;

double static_ratio = 0.2;
ImColor tempColors[] = {
    ImColor(255, 0, 0, 255), // 红色
    ImColor(0, 255, 0, 255), // 绿色
    ImColor(0, 0, 255, 255), // 蓝色
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255)};

bool 识别喷子(int WeaponId)
{
    switch (WeaponId)
    {
    case 104005:
        return true;
        break;
    case 104001:
        return true;
        break;
    case 104002:
        return true;
        break;
    case 104003:
        return true;
        break;
    case 104004:
        return true;
        break;
    case 104100:
        return true;
        break;
    case 104101:
        return true;
        break;
    case 104102:
        return true;
        break;
    }
    return false; // 不是喷子
}

int IsCamera = 0, IsFire = 0, SelfAction = 0, 自身动作 = 0;
long int WorldAddress, ArrayAddress, SelfAddress, Controller, CameraAddress, RootPoint;

void miaobian(float size, int x, int y, ImVec4 color, const char *str)
{
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x + 1.0, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x - 0.1, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y + 1.0), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y - 1.0), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y), ImGui::ColorConvertFloat4ToU32(color), str);
}
void 绘制加粗文本(float size, float x, float y, ImColor color, ImColor color1, const char *str)
{
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x - 0.8, y - 0.8), color1, str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x + 0.8, y + 0.8), color1, str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y), color, str);
}

void DrawPlayerBox(ImDrawList *Draw, float left, float right, float bottom, float top, float x, float y, ImColor color, float size)
{
    float LineSize = size;
    // x距离，y距离
    float xd = x - left;
    float yd = y - top;

    // 左上角
    Draw->AddLine(ImVec2(left, top), ImVec2(left, top + yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(left, top), ImVec2(left + xd / 2, top), color, LineSize);

    // 右上角
    Draw->AddLine(ImVec2(right, top), ImVec2(right, top + yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(right, top), ImVec2(right - xd / 2, top), color, LineSize);

    // 左下角
    Draw->AddLine(ImVec2(left, bottom), ImVec2(left, bottom - yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(left, bottom), ImVec2(left + xd / 2, bottom), color, LineSize);

    // 右下角
    Draw->AddLine(ImVec2(right, bottom), ImVec2(right, bottom - yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(right, bottom), ImVec2(right - xd / 2, bottom), color, LineSize);
}

bool jincang(const char *gname, char **name)
{
    // ALOGD("判断类名%s",ClassName);
    string str(gname);

    if (str.find("perPeopleSkill") != std::string::npos)
    {
        *name = "金仓";
        return true;
    }
    return false;
}

bool ImGuiELGS::所有物品(char *gname, char **name)
{
    /*  if(类名){


              *name=gname;

              return true;
          }*/

    if (地铁 && (strstr(gname, "EscapeBox_SupplyBox_L") || strstr(gname, "EscapeBoxHight_SupplyBox")))
    {
        *name = "物资箱子";
        return true;
    }
    if (imguiswitch_information.boolswitch[53])
    {
        if (strstr(gname, "BP_Ammo_556mm_Pickup_C"))
        {

            *name = "5.56MM";

            return true;
        }
    }
    if (imguiswitch_information.boolswitch[54])
    {
        if (strstr(gname, "BP_Ammo_762mm_Pickup_C"))
        {
            *name = "7.62MM";

            return true;
        }
    }
    // 刀
    if (strstr(gname, "BP_WEP_Pan_Pickup_C"))
    {

        *name = "平底锅";

        return true;
    }
    if (imguiswitch_information.boolswitch[588])
    {
        if (strstr(gname, "Flaregun"))
        {
            *name = "信号枪";

            return true;
        }
        if (strstr(gname, "Flare"))
        {
            *name = "召回/信号枪";

            return true;
        }
        if (strstr(gname, "BP_Pistol_Flaregun_Wrapper_C"))
        {
            *name = "信号枪";

            return true;
        }
        if (strstr(gname, "RevivalCard_PickUp_C"))
        {
            *name = "通用召回信标";

            return true;
        }

        if (strstr(gname, "BP_Ammo_Flare_Pickup_C"))
        {
            *name = "信号弹";

            return true;
        }
    }
    // 显示步枪img
    if (imguiswitch_information.boolswitch[56])
    {

        if (strstr(gname, "Pistol_Flaregun_Wrapper"))
        {

            *name = "信号枪";

            return true;
        }
        if (strstr(gname, "BP_Rifle_AKM_Wrapper_C"))
        {

            *name = "AKM";

            return true;
        }
        if (strstr(gname, "BP_Rifle_M416_Wrapper_C"))
        {

            *name = "M416";

            return true;
        }
        if (strstr(gname, "BP_Rifle_M16A4_Wrapper_C"))
        {

            *name = "M16A4";

            return true;
        }
        if (strstr(gname, "BP_Rifle_SCAR_Wrapper_C"))
        {

            *name = "SCAR";

            return true;
        }
        if (strstr(gname, "BP_Rifle_QBZ_Wrapper_C"))
        {

            *name = "QBZ";

            return true;
        }
        if (strstr(gname, "BP_Rifle_G36_Wrapper_C"))
        {

            *name = "G36C";

            return true;
        }
        if (strstr(gname, "BP_Rifle_M762_Wrapper_C"))
        {

            *name = "M762";

            return true;
        }
        if (strstr(gname, "BP_Rifle_Groza_Wrapper_C"))
        {

            *name = "Groza";

            return true;
        }
        if (strstr(gname, "BP_Rifle_AUG_Wrapper_C"))
        {

            *name = "AUG";

            return true;
        }
        if (strstr(gname, "BP_Other_Dp-28_Wrapper_C"))
        {

            *name = "Dp-28";

            return true;
        }
        if (strstr(gname, "BP_Other_M249_Wrapper_C"))
        {

            *name = "M249";

            return true;
        }
        if (strstr(gname, "BP_Sniper_QBU_Wrapper_C"))
        {

            *name = "QBU";

            return true;
        }
    }
    // 显示冲锋枪
    if (showSubmachine)
    {

        if (strstr(gname, "BP_MachineGun_UMP9_Wrapper_C"))
        {

            *name = "UMP9";

            return true;
        }
        if (strstr(gname, "BP_MachineGun_TommyGun_Wrapper_C"))
        {

            *name = "汤姆逊";

            return true;
        }
        if (strstr(gname, "BP_MachineGun_PP19_Wrapper_C"))
        {

            *name = "PP19";

            return true;
        }
        if (strstr(gname, "BP_MachineGun_Uzi_Wrapper_C"))
        {

            *name = "Uzi";

            return true;
        }
        if (strstr(gname, "BP_MachineGun_Vector_Wrapper_C"))
        {

            *name = "Vector";

            return true;
        }
    }
    // 显示狙击枪
    if (showSniper)
    {

        if (strstr(gname, "BP_Sniper_Kar98k_Wrapper_C"))
        {

            *name = "Kar98k";

            return true;
        }
        if (strstr(gname, "BP_Sniper_Mini14_Wrapper_C"))
        {

            *name = "Mini14";

            return true;
        }
        if (strstr(gname, "BP_Sniper_SKS_Wrapper_C"))
        {

            *name = "SKS";

            return true;
        }
        if (strstr(gname, "BP_Sniper_M24_Wrapper_C"))
        {

            *name = "M24";

            return true;
        }
        if (strstr(gname, "BP_rifle_Mk47_Wrapper_C"))
        {

            *name = "Mk47";

            return true;
        }
        if (strstr(gname, "BP_WEP_Mk14_Pickup_C"))
        {

            *name = "MK14";

            return true;
        }
        if (strstr(gname, "BP_Sniper_AWM_Wrapper_C"))
        {

            *name = "AWM";

            return true;
        }
        if (strstr(gname, "BP_Sniper_SLR_Wrapper_C"))
        {

            *name = "SLR";

            return true;
        }
    }
    // 倍镜
    if (showMirror)
    {

        if (strstr(gname, "BP_MZJ_8X_Pickup_C"))
        {

            *name = "8倍";

            return true;
        }
        if (strstr(gname, "BP_MZJ_6X_Pickup_C"))
        {

            *name = "6倍";

            return true;
        }
        if (strstr(gname, "BP_MZJ_4X_Pickup_C"))
        {

            *name = "4倍";

            return true;
        }
        if (strstr(gname, "BP_MZJ_3X_Pickup_C"))
        {

            *name = "3倍";

            return true;
        }
        if (strstr(gname, "BP_MZJ_2X_Pickup_C"))
        {

            *name = "2倍";

            return true;
        }
        if (strstr(gname, "BP_MZJ_HD_Pickup_C"))
        {

            *name = "红点";

            return true;
        }
        if (strstr(gname, "BP_MZJ_QX_Pickup_C"))
        {

            *name = "全息";

            return true;
        }
    }
    // 配件
    if (showExpansion)
    {
        ;
        if (strstr(gname, "BP_DJ_Large_EQ_Pickup_C"))
        {

            *name = "步枪快速扩容";

            return true;
        }
        if (strstr(gname, "BP_DJ_Large_E_Pickup_C"))
        {

            *name = "步枪扩容";

            return true;
        }
        if (strstr(gname, "BP_DJ_Sniper_EQ_Pickup_C"))
        {

            *name = "击狙枪快速扩容";

            return true;
        }
        if (strstr(gname, "BP_DJ_Sniper_E_Pickup_C"))
        {

            *name = "击狙枪扩容";

            return true;
        }
    }
    // 其他配件
    if (showOtherParts)
    {

        if (strstr(gname, "BP_QK_Large_Suppressor_Pickup_C"))
        {

            *name = "步枪消音";

            return true;
        }
        if (strstr(gname, "BP_QK_Sniper_Suppressor_Pickup_C"))
        {

            *name = "击狙枪消音器";

            return true;
        }
        if (strstr(gname, "BP_QT_Sniper_Pickup_C"))
        {

            *name = "击狙枪托腮板";

            return true;
        }
        if (strstr(gname, "BP_ZDD_Sniper_Pickup_C"))
        {

            *name = "击狙枪子弹带";

            return true;
        }
        if (strstr(gname, "BP_QK_Large_Compensator_Pickup_C"))
        {

            *name = "步枪枪口补偿";

            return true;
        }
        if (strstr(gname, "BP_QK_Sniper_Compensator_Pickup_C"))
        {

            *name = "狙擊槍補償";

            return true;
        }
        if (strstr(gname, "BP_WB_Vertical_Pickup_C"))
        {

            *name = "步槍垂直握把";

            return true;
        }
        if (strstr(gname, "BP_QT_A_Pickup_C"))
        {

            *name = "步槍槍托";

            return true;
        }
        if (strstr(gname, "BP_WB_Angled_Pickup_C"))
        {

            *name = "步槍直角握把";

            return true;
        }
        if (strstr(gname, "BP_WB_ThumbGrip_Pickup_C"))
        {

            *name = "步槍拇指握把";

            return true;
        }
    }
    // 药品显示
    if (imguiswitch_information.boolswitch[55])
    {
        if (strstr(gname, "Injection_Pickup_C"))
        {

            *name = "肾上腺素";

            return true;
        }
        if (strstr(gname, "Firstaid_Pickup_C"))
        {

            *name = "急救包";

            return true;
        }
        if (strstr(gname, "FirstAidbox_Pickup_C"))
        {

            *name = "医疗箱";

            return true;
        }
        if (strstr(gname, "Pills_Pickup_C"))
        {

            *name = "止疼药";

            return true;
        }
        if (strstr(gname, "Drink_Pickup_C"))
        {

            *name = "饮料";

            return true;
        }
        if (strstr(gname, "Bandage_Pickup_C"))
        {

            *name = "绷带";

            return true;
        }
    }
    // 甲和头盔
    if (showArmor)
    {
        if (strstr(gname, "PickUp_BP_Helmet_Lv3_C"))
        {

            *name = "三级头";

            return true;
        }
        if (strstr(gname, "PickUp_BP_Armor_Lv3_C"))
        {

            *name = "三级甲";

            return true;
        }
        if (strstr(gname, "PickUp_BP_Bag_Lv3_B_C") || strstr(gname, "PickUp_BP_Bag_Lv3_A_C") || strstr(gname, "PickUp_BP_Bag_Lv3_C"))
        {

            *name = "三级包";

            return true;
        }
    }
    if (imguiswitch_information.boolswitch[589])
    {
        if (strstr(gname, "CharacterDeadInventoryBox_") || strstr(gname, "BP_SuperPeopleDeadBox_C"))
        {

            *name = "骨灰盒";

            return true;
        }
    }
    if (imguiswitch_information.boolswitch[590])
    {
        if (strstr(gname, "_AirDropPlane_C"))
        {
            *name = "飞机";

            return true;
        }
        if (strstr(gname, "AirDrop"))
        {
            *name = "空投";

            return true;
        }
        if (strstr(gname, "_AirDropPlane_SuperPeople_C") || strstr(gname, "_AirDropBox_C") || strstr(gname, "_AirDropBox_SuperPeople_C"))
        {
            *name = "空投";

            return true;
        }
        if (strstr(gname, "SuperPeopleSkillReplace Actor1_BP"))
        {

            *name = "金叉";

            return true;
        }
    }

    return false;
}

ImTextureID ImAgeHeadFile(const unsigned char *buf, int len)
{
    int w, h, n;
    stbi_uc *data = stbi_png_load_from_memory(buf, len, &w, &h, &n, 0);
    GLuint texture;
    glGenTextures(1, &texture);
    glEnable(GL_TEXTURE_2D);
    glBindTexture(GL_TEXTURE_2D, texture);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);
    if (n == 3)
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, w, h, 0, GL_RGB, GL_UNSIGNED_BYTE, data);
    }
    else
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, w, h, 0, GL_RGBA, GL_UNSIGNED_BYTE, data);
    }
    stbi_image_free(data);
    ImTextureID image_id = (ImTextureID)(GLuint *)texture;
    return image_id;
}

bool 获取枪械信息1(int 枪械编码, ImTextureID *图片名称, char **枪械名字)
{

    if (枪械编码 <= 0 || 枪械编码 >= 100086110 || 枪械编码 == 108001 || 枪械编码 == 108002 || 枪械编码 == 108003 || 枪械编码 == 108004)
    {
        *图片名称 = hand_hold.拳头;
        *枪械名字 = "拳头";
        return true;
    }
    if (枪械编码 == 101001 || 枪械编码 == 1010011 || 枪械编码 == 1010012 || 枪械编码 == 1010013 || 枪械编码 == 1010014 || 枪械编码 == 1010015 || 枪械编码 == 1010016 || 枪械编码 == 1010017)
    {
        *图片名称 = hand_hold.AKM0;
        *枪械名字 = "AKM步枪";
        return true;
    }
    if (枪械编码 == 101002 || 枪械编码 == 1010021 || 枪械编码 == 1010022 || 枪械编码 == 1010023 || 枪械编码 == 1010024 || 枪械编码 == 1010025 || 枪械编码 == 1010026 || 枪械编码 == 1010027)
    {
        *图片名称 = hand_hold.M16A;
        *枪械名字 = "M16A4步枪";
        return true;
    }
    if (枪械编码 == 101003 || 枪械编码 == 1010031 || 枪械编码 == 1010032 || 枪械编码 == 1010033 || 枪械编码 == 1010034 || 枪械编码 == 1010035 || 枪械编码 == 1010036 || 枪械编码 == 1010037)
    {
        *图片名称 = hand_hold.死噶;
        *枪械名字 = "SCAR-L步枪";
        return true;
    }
    if (枪械编码 == 101004 || 枪械编码 == 1010041 || 枪械编码 == 1010042 || 枪械编码 == 1010043 || 枪械编码 == 1010044 || 枪械编码 == 1010045 || 枪械编码 == 1010046 || 枪械编码 == 1010047)
    {
        *图片名称 = hand_hold.M416;
        *枪械名字 = "M416步枪";
        return true;
    }
    if (枪械编码 == 101005 || 枪械编码 == 1010051 || 枪械编码 == 1010052 || 枪械编码 == 1010053 || 枪械编码 == 1010054 || 枪械编码 == 1010055 || 枪械编码 == 1010056 || 枪械编码 == 1010057)
    {
        *图片名称 = hand_hold.狗砸;
        *枪械名字 = "狗砸步枪";
        return true;
    }
    if (枪械编码 == 101006 || 枪械编码 == 1010061 || 枪械编码 == 1010062 || 枪械编码 == 1010063 || 枪械编码 == 1010064 || 枪械编码 == 1010065 || 枪械编码 == 10100166 || 枪械编码 == 1010067)
    {
        *图片名称 = hand_hold.aug;
        *枪械名字 = "AUG步枪";
        return true;
    }
    if (枪械编码 == 101008 || 枪械编码 == 1010081 || 枪械编码 == 1010082 || 枪械编码 == 1010083 || 枪械编码 == 1010084 || 枪械编码 == 1010085 || 枪械编码 == 1010086 || 枪械编码 == 1010087)
    {
        *图片名称 = hand_hold.M762;
        *枪械名字 = "猛男步枪";
        return true;
    }
    if (枪械编码 == 101009 || 枪械编码 == 1010091 || 枪械编码 == 1010092 || 枪械编码 == 1010093 || 枪械编码 == 1010094 || 枪械编码 == 1010095 || 枪械编码 == 1010096 || 枪械编码 == 1010097)
    {
        *图片名称 = hand_hold.mk47;
        *枪械名字 = "Mk-47步枪";
        return true;
    }
    if (枪械编码 == 101010 || 枪械编码 == 1010101 || 枪械编码 == 1010102 || 枪械编码 == 1010103 || 枪械编码 == 1010104 || 枪械编码 == 1010105 || 枪械编码 == 1010106 || 枪械编码 == 1010107)
    {
        *图片名称 = hand_hold.g36c;
        *枪械名字 = "G36C步枪";
        return true;
    }
    if (枪械编码 == 102001 || 枪械编码 == 1020011 || 枪械编码 == 1020012 || 枪械编码 == 1020013 || 枪械编码 == 1020014 || 枪械编码 == 1020015)
    {
        *图片名称 = hand_hold.乌兹;
        *枪械名字 = "乌兹冲锋枪";
        return true;
    }
    if (枪械编码 == 102002 || 枪械编码 == 1020021 || 枪械编码 == 1020022 || 枪械编码 == 1020023 || 枪械编码 == 1020024 || 枪械编码 == 1020025)
    {
        *图片名称 = hand_hold.ump;
        *枪械名字 = "UMP-45冲锋枪";
        return true;
    }
    if (枪械编码 == 102003 || 枪械编码 == 1020031 || 枪械编码 == 1020032 || 枪械编码 == 1020033 || 枪械编码 == 1020034 || 枪械编码 == 1020035 || 枪械编码 == 1020036 || 枪械编码 == 1020037)
    {
        *图片名称 = hand_hold.维克托;
        *枪械名字 = "Vector冲锋枪";
        return true;
    }
    if (枪械编码 == 102004 || 枪械编码 == 1020041 || 枪械编码 == 1020042 || 枪械编码 == 1020043 || 枪械编码 == 1020044 || 枪械编码 == 1020045 || 枪械编码 == 1020046 || 枪械编码 == 1020047)
    {
        *图片名称 = hand_hold.汤姆逊;
        *枪械名字 = "汤姆逊冲锋枪";
        return true;
    }
    if (枪械编码 == 102005 || 枪械编码 == 1020051 || 枪械编码 == 1020052 || 枪械编码 == 1020053 || 枪械编码 == 1020054 || 枪械编码 == 1020055 || 枪械编码 == 1020056 || 枪械编码 == 1020057)
    {
        *图片名称 = hand_hold.野牛;
        *枪械名字 = "野牛冲锋枪";
        return true;
    }
    if (枪械编码 == 103002 || 枪械编码 == 1030021 || 枪械编码 == 1030022 || 枪械编码 == 1030023 || 枪械编码 == 1030024 || 枪械编码 == 1030025 || 枪械编码 == 1030026 || 枪械编码 == 1030027)
    {
        *图片名称 = hand_hold.m24;
        *枪械名字 = "M24狙鸡枪";
        return true;
    }
    if (枪械编码 == 103003 || 枪械编码 == 1030031 || 枪械编码 == 1030032 || 枪械编码 == 1030033 || 枪械编码 == 1030034 || 枪械编码 == 1030035 || 枪械编码 == 1030036 || 枪械编码 == 1030037)
    {
        *图片名称 = hand_hold.awm;
        *枪械名字 = "AWM狙鸡枪";
        return true;
    }
    if (枪械编码 == 103007 || 枪械编码 == 1030071 || 枪械编码 == 1030072 || 枪械编码 == 1030073 || 枪械编码 == 1030074 || 枪械编码 == 1030075 || 枪械编码 == 1030076 || 枪械编码 == 1030077)
    {
        *图片名称 = hand_hold.妹控;
        *枪械名字 = "妹控狙击步枪";
        return true;
    }
    if (枪械编码 == 107001 || 枪械编码 == 107007 || 枪械编码 == 1070071 || 枪械编码 == 1070072 || 枪械编码 == 1070073 || 枪械编码 == 1070074 || 枪械编码 == 1070075 || 枪械编码 == 1070076 || 枪械编码 == 1070077 || 枪械编码 == 107008)
    {
        *图片名称 = hand_hold.复合弓;
        *枪械名字 = "大弓！";
        return true;
    }
    if (枪械编码 == 102105)
    {
        *图片名称 = hand_hold.p90;
        *枪械名字 = "p90";
        return true;
    }
    if (枪械编码 == 602003)
    {
        *图片名称 = hand_hold.燃烧瓶;
        *枪械名字 = "燃烧瓶";
        return true;
    }
    if (枪械编码 == 602004)
    {
        *图片名称 = hand_hold.手雷弹;
        *枪械名字 = "手雷";
        return true;
    }
    if (枪械编码 == 602002)
    {
        *图片名称 = hand_hold.烟雾弹;
        *枪械名字 = "烟雾弹";
        return true;
    }
    if (枪械编码 == 101012)
    {
        *图片名称 = hand_hold.蜜罐;
        *枪械名字 = "蜜罐突击步枪";
        return true;
    }
    return false;
}

void 加载图片()
{
    hand_hold.M16A = ImAgeHeadFile(m16, sizeof(m16));
    hand_hold.拳头 = ImAgeHeadFile(qt, sizeof(qt));
    hand_hold.M762 = ImAgeHeadFile(m762, sizeof(m762));
    hand_hold.M416 = ImAgeHeadFile(m416, sizeof(m416));
    hand_hold.AKM0 = ImAgeHeadFile(akm, sizeof(akm));
    hand_hold.维克托 = ImAgeHeadFile(vkt, sizeof(vkt));
    hand_hold.M249 = ImAgeHeadFile(m249, sizeof(m249));
    hand_hold.狗砸 = ImAgeHeadFile(gz, sizeof(gz));
    hand_hold.mk47 = ImAgeHeadFile(mk47, sizeof(mk47));
    hand_hold.m24 = ImAgeHeadFile(m24, sizeof(m24));
    hand_hold.awm = ImAgeHeadFile(awm, sizeof(awm));
    hand_hold.mini = ImAgeHeadFile(mini, sizeof(mini));
    hand_hold.乌兹 = ImAgeHeadFile(uzi, sizeof(uzi));
    hand_hold.野牛 = ImAgeHeadFile(yn1, sizeof(yn1));
    hand_hold.g36c = ImAgeHeadFile(g36c, sizeof(g36c));
    hand_hold.aug = ImAgeHeadFile(aug, sizeof(aug));
    hand_hold.ump = ImAgeHeadFile(ump, sizeof(ump));
    hand_hold.大炮 = ImAgeHeadFile(slr, sizeof(slr));
    hand_hold.汤姆逊 = ImAgeHeadFile(tmx, sizeof(tmx));
    hand_hold.死噶 = ImAgeHeadFile(scar, sizeof(scar));
    hand_hold.妹控 = ImAgeHeadFile(mk14, sizeof(mk14));
    hand_hold.骨灰盒 = ImAgeHeadFile(棺材, sizeof(棺材));
    hand_hold.箱子开 = ImAgeHeadFile(箱子开, sizeof(箱子开));
    hand_hold.箱子关 = ImAgeHeadFile(箱子关, sizeof(箱子关));
    hand_hold.车辆 = ImAgeHeadFile(车辆, sizeof(车辆));
    hand_hold.狗子 = ImAgeHeadFile(狗子, sizeof(狗子));
    hand_hold.真人 = ImAgeHeadFile(真人, sizeof(真人));
    hand_hold.人机 = ImAgeHeadFile(人机, sizeof(人机));
    hand_hold.警告 = ImAgeHeadFile(警告, sizeof(警告));
    hand_hold.背景 = ImAgeHeadFile(背景, sizeof(背景));
    hand_hold.复合弓 = ImAgeHeadFile(复合弓, sizeof(复合弓));
    hand_hold.p90 = ImAgeHeadFile(p90, sizeof(p90));
    hand_hold.燃烧瓶 = ImAgeHeadFile(rsp, sizeof(rsp));
    hand_hold.手雷弹 = ImAgeHeadFile(手雷弹, sizeof(手雷弹));
    hand_hold.烟雾弹 = ImAgeHeadFile(ywd, sizeof(ywd));
    hand_hold.蜜罐 = ImAgeHeadFile(蜜罐, sizeof(蜜罐));
}

void ImGuiELGS::InitializeFoundationConfiGuration()
{
    OneTimeFrame = 90;

    touch_information.TouchRadius = 0.06f;
    touch_information.TouchPoints.x = 0.8f;
    touch_information.TouchPoints.y = 0.6f;

    touch_information.floatswitch[0] = 900.f;
    touch_information.floatswitch[1] = 0.06f;
    touch_information.floatswitch[2] = 0.23f;
    touch_information.floatswitch[3] = 0.45f;

    imguiswitch_information.intswitch[50] = 0;
    imguiswitch_information.intswitch[51] = 0;
    imguiswitch_information.intswitch[52] = 0;
    imguiswitch_information.intswitch[53] = 0;
    imguiswitch_information.intswitch[54] = 0;
    imguiswitch_information.intswitch[90] = 200;

    imguiswitch_information.floatswitch[40] = -493.f;
    imguiswitch_information.floatswitch[41] = -126.f;
    imguiswitch_information.floatswitch[42] = 100.f;
    imguiswitch_information.floatswitch[43] = 134.12f;
    imguiswitch_information.floatswitch[50] = 150.f;
    imguiswitch_information.floatswitch[51] = 350.f;
    imguiswitch_information.floatswitch[52] = 0.12f;
    imguiswitch_information.floatswitch[53] = 2.13f;
    imguiswitch_information.floatswitch[54] = 1.87f;
    imguiswitch_information.floatswitch[55] = 1.1f;
    ycsz = 2.05f;
    ycsd = 1.83f;
    ycsp = 1.0f;
    imguiswitch_information.floatswitch[56] = 500.f;
    imguiswitch_information.floatswitch[57] = 1.70f;

    imguiswitch_information.colorswitch[0] = ImColor(255, 255, 255, 255);
    imguiswitch_information.colorswitch[1] = ImColor(255, 255, 255, 255);
    imguiswitch_information.colorswitch[2] = ImColor(255, 255, 255, 255);
    imguiswitch_information.colorswitch[3] = ImColor(255, 50, 50, 200);   // 真人
    imguiswitch_information.colorswitch[4] = ImColor(230, 230, 50, 200);  // 掩体
    imguiswitch_information.colorswitch[5] = ImColor(50, 255, 50, 200);   // 人机
    imguiswitch_information.colorswitch[6] = ImColor(200, 200, 200, 200); // 人机
    imguiswitch_information.colorswitch[7] = ImColor(255, 255, 255, 255);
    imguiswitch_information.colorswitch[8] = ImColor(255, 255, 255, 255);
    imguiswitch_information.colorswitch[9] = ImColor(255, 255, 255, 255);
}

VecTor2 ImGuiELGS::GetTouchScreenDimension(int Handle)
{
    int TouchScreenX[6] = {0};
    int TouchScreenY[6] = {0};
    ioctl(Handle, EVIOCGABS(ABS_MT_POSITION_X), TouchScreenX);
    ioctl(Handle, EVIOCGABS(ABS_MT_POSITION_Y), TouchScreenY);
    return VecTor2(TouchScreenX[2], TouchScreenY[2]);
}

ImTextureID ImGuiELGS::texturereadsfile(const unsigned char *buffer, int length)
{
    int w = 0;
    int h = 0;
    int n = 0;
    GLuint texture;
    stbi_uc *bufferdata = stbi_png_load_from_memory(buffer, length, &w, &h, &n, 0);
    glGenTextures(1, &texture);
    glEnable(GL_TEXTURE_2D);
    glBindTexture(GL_TEXTURE_2D, texture);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);
    if (n == 3)
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, w, h, 0, GL_RGB, GL_UNSIGNED_BYTE, bufferdata);
    }
    else
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, w, h, 0, GL_RGBA, GL_UNSIGNED_BYTE, bufferdata);
    }
    stbi_image_free(bufferdata);
    ImTextureID image_id = (GLuint *)texture;
    return image_id;
}

string ImGuiELGS::GetTouchScreenDeviceFile()
{
    const char *command = "getevent";
    FILE *PIPE = popen(command, "r");
    if (!PIPE)
    {
        return "";
    }
    int lineCount = 0;
    string result = "";
    char buffer[128] = "";
    cout << "\033[35;1m[+] 请滑动一下屏幕\033[30;1m" << endl;
    while (!feof(PIPE))
    {
        if (fgets(buffer, 128, PIPE) != NULL)
        {
            string line(buffer);
            if (line.find("/dev/input/event") != string::npos)
            {
                result += line;
                lineCount++;
            }
            if (lineCount == 20)
            {
                break;
            }
        }
    }
    pclose(PIPE);
    regex pattern("/dev/input/event\\d+");
    sregex_iterator iter(result.begin(), result.end(), pattern);
    sregex_iterator end;
    string lastEventLine = "";
    while (iter != end)
    {
        lastEventLine = iter->str();
        ++iter;
    }
    return lastEventLine;
}

bool ImGuiELGS::ImGuiGetSurfaceWindow()
{

    int output = 1;
    cout << "\033[35;1m[+] 是否开启防录屏[1开启防录屏/2不开防录屏]: \033[30;1m";
    cin >> output;
    if (output == 1)
    {
        native_window = Android::NativeWindowCreator::CreateSurfaceNativeWindow("Broken", resolution_information.FixedScreenWidth + resolution_information.FixedScreenHeiht, resolution_information.FixedScreenHeiht + resolution_information.FixedScreenWidth, 0x40);
    }
    else if (output == 2)
    {
        native_window = Android::NativeWindowCreator::CreateSurfaceNativeWindow("Broken", resolution_information.FixedScreenWidth + resolution_information.FixedScreenHeiht, resolution_information.FixedScreenHeiht + resolution_information.FixedScreenWidth, 0x00);
    }
    else
    {
        cout << "\033[31;1m[+] 输入错误\033[30;1m" << endl;
        return false;
    }
    ANativeWindow_acquire(native_window);
    display = eglGetDisplay(EGL_DEFAULT_DISPLAY);
    if (display == EGL_NO_DISPLAY)
    {
        return false;
    }
    if (eglInitialize(display, 0, 0) != EGL_TRUE)
    {
        return false;
    }
    EGLint num_config = 0;
    const EGLint attribList[] = {EGL_SURFACE_TYPE, EGL_WINDOW_BIT, EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT, EGL_BLUE_SIZE, 8, EGL_GREEN_SIZE, 8, EGL_RED_SIZE, 8, EGL_ALPHA_SIZE, 8, EGL_BUFFER_SIZE, 32, EGL_DEPTH_SIZE, 24, EGL_STENCIL_SIZE, 8, EGL_NONE};
    if (eglChooseConfig(display, attribList, nullptr, 0, &num_config) != EGL_TRUE)
    {
        return false;
    }
    if (!eglChooseConfig(display, attribList, &config, 1, &num_config))
    {
        return false;
    }
    EGLint egl_format;
    eglGetConfigAttrib(display, config, EGL_NATIVE_VISUAL_ID, &egl_format);
    ANativeWindow_setBuffersGeometry(native_window, 0, 0, egl_format);
    const EGLint attrib_list[] = {EGL_CONTEXT_CLIENT_VERSION, 3, EGL_NONE};
    context = eglCreateContext(display, config, EGL_NO_CONTEXT, attrib_list);
    if (context == EGL_NO_CONTEXT)
    {
        return false;
    }
    surface = eglCreateWindowSurface(display, config, native_window, nullptr);
    if (surface == EGL_NO_SURFACE)
    {
        return false;
    }
    if (!eglMakeCurrent(display, surface, surface, context))
    {
        return false;
    }
    return true;
}

void ImGuiELGS::ImGuiGetScreenInformation()
{
    touch_information.TouchDeviceFile = open(GetTouchScreenDeviceFile().c_str(), O_RDONLY | O_SYNC | O_NONBLOCK);
    if (touch_information.TouchDeviceFile <= 0)
    {
        cout << "\033[31;1m[-] 获取触摸设备文件失败\033[30;1m" << endl;
        return;
    }
    else
    {
        touch_information.TouchScreenSize = GetTouchScreenDimension(touch_information.TouchDeviceFile);
    }
    thread *screeninformationthread = new thread([this]
                                                 {
		static int ORIENTATIONTEMP = 0;
        for (;;) {
			displayinformation = Android::NativeWindowCreator::GetDisplayInfo();
			if (!ORIENTATIONTEMP) {
				resolution_information.Orientation = displayinformation.orientation;
				ORIENTATIONTEMP = 1;
			}
			resolution_information.Width = displayinformation.upwidth / 2;
			resolution_information.Heiht = displayinformation.upheight / 2;
			resolution_information.ScreenWidth = displayinformation.upwidth;
			resolution_information.ScreenHeiht = displayinformation.upheight;
			resolution_information.FixedScreenWidth = displayinformation.width;
			resolution_information.FixedScreenHeiht = displayinformation.height;
			if (displayinformation.orientation != resolution_information.Orientation) {
				touch_information.TouchOrientationControl = true;
				resolution_information.Orientation = displayinformation.orientation;
			}
			this_thread::sleep_for(0.1s);
        } });
    screeninformationthread->detach();
    return;
}

void ImGuiELGS::ImGuiWindowStar()
{
    ImGui_ImplOpenGL3_NewFrame();
    ImGui_ImplAndroid_NewFrame(resolution_information.FixedScreenWidth + resolution_information.FixedScreenHeiht, resolution_information.FixedScreenHeiht + resolution_information.FixedScreenWidth);
    ImGui::NewFrame();
}

void ImGuiELGS::ImGuiWindowExit()
{
    glViewport(0, 0, ImGui::GetIO().DisplaySize.x, ImGui::GetIO().DisplaySize.y);
    glClearColor(0, 0, 0, 0);
    glClear(GL_COLOR_BUFFER_BIT);
    glFlush();
    if (display == EGL_NO_DISPLAY)
    {
        return;
    }
    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
    eglSwapBuffers(display, surface);
}

void ImGuiELGS::ImGuiWindowRele()
{
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplAndroid_Shutdown();
    ImGui::DestroyContext();
    if (display != EGL_NO_DISPLAY)
    {
        eglMakeCurrent(display, EGL_NO_SURFACE, EGL_NO_SURFACE, EGL_NO_CONTEXT);
        if (context != EGL_NO_CONTEXT)
        {
            eglDestroyContext(display, context);
        }
        if (surface != EGL_NO_SURFACE)
        {
            eglDestroySurface(display, surface);
        }
        eglTerminate(display);
    }
    display = EGL_NO_DISPLAY;
    context = EGL_NO_CONTEXT;
    surface = EGL_NO_SURFACE;
    ANativeWindow_release(native_window);
}

void ImGuiELGS::ImGuiInItialization()
{
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    // ImGui::StyleColorsClassic();
    ImGuiStyle &Style = ImGui::GetStyle();
    ImGuiIO &io = ImGui::GetIO();
    (void)io;
    io.FontGlobalScale = 1.3f;

    // 窗口背景 - 深灰色
    Style.GrabRounding = 5;
    ImGui::GetStyle().ScrollbarSize = 30.f;
    ImGui::GetStyle().WindowRounding = 25.f;

    ImGui::GetStyle().ChildRounding = 23.0f;     // 设置为10.0f，你可以根据需要调整这个值
    Style.FrameRounding = 23.0f;                 // 控件圆角
    ImGui::GetStyle().GrabRounding = 23.0f;      // 滑块圆角
    ImGui::GetStyle().ScrollbarRounding = 23.0f; // 滚动条的圆角

    Style.Colors[ImGuiCol_Text] = ImVec4(0.0f, 0.0f, 0.0f, 1.0f); // 全局文本颜色设置为黑色
                                                                  // 更改滑动条背景颜色
    Style.Colors[ImGuiCol_SliderGrab] = ImVec4(0.68f, 0.85f, 0.90f, 1.0f);

    // 更改滑动条背景激活颜色
    Style.Colors[ImGuiCol_SliderGrabActive] = ImVec4(0.68f, 0.85f, 0.90f, 0.8f);

    // 更改滑动条轨道颜色
    Style.Colors[ImGuiCol_FrameBg] = ImVec4(0.68f, 0.85f, 0.90f, 0.8f);

    // 更改滑动条轨道激活颜色
    Style.Colors[ImGuiCol_FrameBgActive] = ImVec4(0.68f, 0.85f, 0.90f, 0.8f);
    ImVec4 *colors = Style.Colors;
    colors[ImGuiCol_TextDisabled] = ImVec4(0.49f, 0.50f, 0.53f, 1.00f);
    colors[ImGuiCol_WindowBg] = ImVec4(1.0f, 1.0f, 1.0f, 0.8f);
    colors[ImGuiCol_ChildBg] = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);

    colors[ImGuiCol_Button] = ImVec4(0.68f, 0.85f, 0.90f, 1.0f);
    colors[ImGuiCol_ButtonHovered] = ImVec4(0.68f, 0.85f, 0.90f, 0.8f);
    colors[ImGuiCol_ButtonActive] = ImVec4(0.68f, 0.85f, 0.90f, 0.6f);
    Style.WindowBorderSize = 10.00f;

    // 调整透明度和颜色以满足特定需求
    InitializeFoundationConfiGuration();
    ImGui_ImplAndroid_Init(native_window);
    ImGui_ImplOpenGL3_Init("#version 300 es");
    ImGui::GetIO().Fonts->AddFontFromMemoryTTF((void *)FontFile, Fontsize, 28.f, NULL, ImGui::GetIO().Fonts->GetGlyphRangesChineseFull());
    ImGui::GetIO().Fonts->AddFontFromMemoryTTF((void *)icons_binary, sizeof(icons_binary), 34.f);
    ImGui::GetIO().Fonts->AddFontFromMemoryTTF((void *)font_bold_binary, sizeof(font_bold_binary), 34.f);
    IM_ASSERT(ImGui::GetIO().Fonts != NULL);
    LoadFile(".HP_SIVE");
    加载图片();
}

string 获取人物动作(int dz)
{
    switch (dz)
    {
    // 站立一系列
    case 16:
        return "[站立]无动作";
        break;
    case 17:
    case 19:
        return "[站立]移动";
        break;
    case 1040:
        return "[站立]开镜";
        break;
    case 1041:
        return "[站立]开镜移动";
        break;
    case 1296:
        return "[站立]开镜开火";
        break;
    case 1297:
        return "[站立]开镜开火移动";
        break;
    case 4112:
        return "[站立]探头";
        break;
    case 4113:
        return "[站立]探头移动";
        break;
    case 272:
        return "[站立]开火";
        break;
    case 273:

        return "[站立]开火移动";
        break;
    case 4368:
        return "[站立]探头开火";
        break;
    case 4369:
        return "[站立]探头开火移动";
        break;
    case 5136:
        return "[站立]探头开镜";
        break;
    case 5137:
        return "[站立]探头开镜移动";
        break;
    case 5392:
        return "[站立]探头开镜开火";
        break;
    case 5393:
        return "[站立]探头开镜开火移动";
        break;
    case 32784:
        return "[站立]挥拳";
        break;
    case 32787:
    case 32785:
        return "[站立]挥拳移动";
        break;
    case 528:
        return "[站立]换弹";
        break;
    case 529:

        return "[站立]换弹移动";
        break;
    case 8208:
        return "[站立]换物品";
        break;
    case 8211:
    case 8209:
        return "[站立]换物品移动";
        break;
    case 65552:
        return "[站立]投掷";
        break;
    case 65553:
        return "[站立]投掷移动";
        break;
    case 69648:
        return "[站立]探头投掷";
        break;
    case 69649:
        return "[站立]探头投掷移动";
        break;
    case 262160:
        return "[站立]打药";
        break;
    case 262161:
        return "[站立]打药移动";
        break;
    // 蹲下一系列
    case 32:
        return "[蹲下]无动作";
        break;
    case 33:
    case 35:
        return "[蹲下]移动";
        break;
    case 288:
        return "[蹲下]开火";
        break;
    case 289:

        return "[蹲下]开火移动";
        break;
    case 1312:
        return "[蹲下]开镜开火";
        break;
    case 1313:
        return "[蹲下]开镜开火移动";
        break;
    case 4128:
        return "[蹲下]探头";
        break;
    case 4129:
        return "[蹲下]探头移动";
        break;
    case 1056:
        return "[蹲下]开镜";
        break;
    case 1057:
        return "[蹲下]开镜移动";
        break;
    case 4384:
        return "[蹲下]探头开火";
        break;
    case 4385:
        return "[蹲下]探头开火移动";
        break;
    case 5152:
        return "[蹲下]探头开镜";
        break;
    case 5153:
        return "[蹲下]探头开镜移动";
        break;
    case 5408:
        return "[蹲下]探头开镜开火";
        break;
    case 5409:
        return "[蹲下]探头开镜开火移动";
        break;
    case 32800:
        return "[蹲下]挥拳";
        break;
    case 32803:
    case 32801:
        return "[蹲下]挥拳移动";
        break;
    case 544:
        return "[蹲下]换弹";
        break;
    case 545:
        return "[蹲下]换弹移动";
        break;
    case 8224:
        return "[蹲下]换物品";
        break;
    case 8227:
    case 8225:
        return "[蹲下]换物品移动";
        break;
    case 65568:
        return "[蹲下]投掷";
        break;
    case 65569:
        return "[蹲下]投掷移动";
        break;
    case 69664:
        return "[蹲下]探头投掷";
        break;
    case 69665:
        return "[蹲下]探头投掷移动";
        break;
    case 262176:
        return "[蹲下]打药";
        break;
    case 262177:
        return "[蹲下]打药移动";
        break;
    // 趴下一系列
    case 64:
        return "[趴下]无动作";
        break;
    case 65:
    case 67:

        return "[趴下]移动";
        break;
    case 1088:
        return "[趴下]开镜";
        break;

    case 320:
        return "[趴下]开火";
        break;
    case 1344:
        return "[趴下]开镜开火";
        break;
    case 576:
        return "[趴下]换弹";
        break;

    case 8256:
        return "[趴下]换物品";
        break;
    case 8257:
    case 8259:
        return "[趴下]换物品移动";
        break;
    case 65600:
        return "[趴下]投掷";
        break;
    case 65601:
        return "[趴下]投掷移动";
        break;
    case 262208:
        return "[趴下]打药";
        break;
    // 跳跃一系列
    case 144:
        return "[跳跃]无动作";
        break;
    case 145:
    case 147:
        return "[跳跃]移动";
        break;
    case 32912:
        return "[跳跃]挥拳";
        break;
    case 32915:
        return "[跳跃]挥拳移动";
        break;
    case 400:
        return "[跳跃]开火";
        break;
    case 401:
    case 403:
        return "[跳跃]开火移动";
        break;
    case 656:
        return "[跳跃]换弹";
        break;
    case 657:

        return "[跳跃]换弹移动";
        break;
    case 8336:
        return "[跳跃]换物品";
        break;
    case 8337:
    case 8339:
        return "[跳跃]换物品移动";
        break;
    case 65680:
        return "[跳跃]投掷";
        break;
    case 65681:
        return "[跳跃]投掷移动";
        break;
    // 车一系列
    case 2097168:
        return "[开车]";
        break;
    case 4194320:
        return "[坐车]无动作";
        break;
    case 12582928:
        return "[坐车]探头";
        break;
    case 125183184:
        return "[坐车]开火";
        break;
    case 12583952:
        return "[坐车]开镜";
        break;
    case 12584208:
        return "[坐车]开镜开火";
        break;
    case 12583440:
        return "[坐车]换弹";
        break;
    case 12648464:
        return "[坐车]投掷";
        break;
    // 游泳
    case 16777216:
        return "[游泳]无动作";
        break;
    case 16777217:
    case 16777219:
        return "[游泳]移动";
        break;
    // 倒地

    // 其它一系列
    case 67108880:

        return "爬东西";
        break;

    default:
        return "未知";
        break;
    }
}

char *GetHol(int wuqi)
{
    switch (wuqi)
    {
    // 突击步枪
    case 101008:
        return "M762";
        break;
    case 1010081:
        return "M762破损";
        break;
    case 1010082:
        return "M762修复";
        break;
    case 1010083:
        return "M762完好";
        break;
    case 1010084:
        return "M762改进";
        break;
    case 1010085:
        return "M762精致";
        break;
    case 1010086:
        return "M762独眼蛇";
        break;
    case 1010087:
        return "M762钢铁阵线";
        break;

    case 101001:
        return "AKM";
        break;
    case 1010011:
        return "AKM破损";
        break;
    case 1010012:
        return "AKM修复";
        break;
    case 1010013:
        return "AKM完好";
        break;
    case 1010014:
        return "AKM改进";
        break;
    case 1010015:
        return "AKM精致";
        break;
    case 1010016:
        return "AKM独眼蛇";
        break;
    case 1010017:
        return "AKM钢铁阵线";
        break;

    case 101004:
        return "M416";
        break;
    case 1010041:
        return "M416破损";
        break;
    case 1010042:
        return "M416修复";
        break;
    case 1010043:
        return "M416完好";
        break;
    case 1010044:
        return "M416改进";
        break;
    case 1010045:
        return "M416精致";
        break;
    case 1010046:
        return "M416独眼蛇";
        break;
    case 1010047:
        return "M416钢铁阵线";
        break;

    case 101003:
        return "SCAR-L";
        break;
    case 1010031:
        return "SCAR-L破损";
        break;
    case 1010032:
        return "SCAR-L修复";
        break;
    case 1010033:
        return "SCAR-L完好";
        break;
    case 1010034:
        return "SCAR-L改进";
        break;
    case 1010035:
        return "SCAR-L精致";
        break;
    case 1010036:
        return "SCAR-L独眼蛇";
        break;
    case 1010037:
        return "SCAR-L钢铁阵线";
        break;

    case 101002:
        return "M16A4";
        break;
    case 1010021:
        return "M16A4破损";
        break;
    case 1010022:
        return "M16A4修复";
        break;
    case 1010023:
        return "M16A4完好";
        break;
    case 1010024:
        return "M16A4改进";
        break;
    case 1010025:
        return "M16A4精致";
        break;
    case 1010026:
        return "M16A4独眼蛇";
        break;
    case 1010027:
        return "M16A4钢铁阵线";
        break;

    case 101009:
        return "Mk47";
        break;
    case 1010091:
        return "Mk47破损";
        break;
    case 1010092:
        return "Mk47修复";
        break;
    case 1010093:
        return "Mk47完好";
        break;
    case 1010094:
        return "Mk47改进";
        break;
    case 1010095:
        return "Mk47精致";
        break;
    case 1010096:
        return "Mk47独眼蛇";
        break;
    case 1010097:
        return "Mk47钢铁阵线";
        break;

    case 101006:
        return "AUG";
        break;
    case 1010061:
        return "AUG破损";
        break;
    case 1010062:
        return "AUG修复";
        break;
    case 1010063:
        return "AUG完好";
        break;
    case 1010064:
        return "AUG改进";
        break;
    case 1010065:
        return "AUG精致";
        break;
    case 1010066:
        return "AUG独眼蛇";
        break;
    case 1010067:
        return "AUG钢铁阵线";
        break;

    case 101005:
        return "Groza";
        break;
    case 1010051:
        return "Groza破损";
        break;
    case 1010052:
        return "Groza修复";
        break;
    case 1010053:
        return "Groza完好";
        break;
    case 1010054:
        return "Groza改进";
        break;
    case 1010055:
        return "Groza精致";
        break;
    case 1010056:
        return "Groza独眼蛇";
        break;
    case 1010057:
        return "Groza钢铁阵线";
        break;

    case 101010:
        return "G36C";
        break;
    case 1010101:
        return "G36C破损";
        break;
    case 1010102:
        return "G36C修复";
        break;
    case 1010103:
        return "G36C完好";
        break;
    case 1010104:
        return "G36C改进";
        break;
    case 1010105:
        return "G36C精致";
        break;
    case 1010106:
        return "G36C独眼蛇";
        break;
    case 1010107:
        return "G36C钢铁阵线";
        break;

    case 101007:
        return "QBZ";
        break;
    case 1010071:
        return "QBZ破损";
        break;
    case 1010072:
        return "QBZ修复";
        break;
    case 1010073:
        return "QBZ完好";
        break;
    case 1010074:
        return "QBZ改进";
        break;
    case 1010075:
        return "QBZ精致";
        break;
    case 1010076:
        return "QBZ独眼蛇";
        break;
    case 1010077:
        return "QBZ钢铁阵线";
        break;

    case 101011:
        return "AC-VAL";
        break;
    case 1010111:
        return "AC-VAL破损";
        break;
    case 1010112:
        return "AC-VAL修复";
        break;
    case 1010113:
        return "AC-VAL完好";
        break;
    case 1010114:
        return "AC-VAL改进";
        break;
    case 1010115:
        return "AC-VAL精致";
        break;
    case 1010116:
        return "AC-VAL独眼蛇";
        break;
    case 1010117:
        return "AC-VAL钢铁阵线";
        break;

    case 101012:
        return "蜜獾突击步枪";
        break;
    case 1010121:
        return "蜜獾突击步枪破损";
        break;
    case 1010122:
        return "蜜獾突击步枪修复";
        break;
    case 1010123:
        return "蜜獾突击步枪完好";
        break;
    case 1010124:
        return "蜜獾突击步枪改进";
        break;
    case 1010125:
        return "蜜獾突击步枪精致";
        break;
    case 1010126:
        return "蜜獾突击步枪独眼蛇";
        break;
    case 1010127:
        return "蜜獾突击步枪钢铁阵线";
        break;
    // 连狙
    case 103009:
        return "SLR";
        break;
    case 1030091:
        return "SLR破损";
        break;
    case 1030092:
        return "SLR修复";
        break;
    case 1030093:
        return "SLR完好";
        break;
    case 1030094:
        return "SLR改进";
        break;
    case 1030095:
        return "SLR精致";
        break;
    case 1030096:
        return "SLR独眼蛇";
        break;
    case 1030097:
        return "SLR钢铁阵线";
        break;

    case 103005:
        return "VSS";
        break;
    case 1030051:
        return "VSS破损";
        break;
    case 1030052:
        return "VSS修复";
        break;
    case 1030053:
        return "VSS完好";
        break;
    case 1030054:
        return "VSS改进";
        break;
    case 1030055:
        return "VSS精致";
        break;
    case 1030056:
        return "VSS独眼蛇";
        break;
    case 1030057:
        return "VSS钢铁阵线";
        break;

    case 103006:
        return "Mini14";
        break;
    case 1030061:
        return "Mini14破损";
        break;
    case 1030062:
        return "Mini14修复";
        break;
    case 1030063:
        return "Mini14完好";
        break;
    case 1030064:
        return "Mini14改进";
        break;
    case 1030065:
        return "Mini14精致";
        break;
    case 1030066:
        return "Mini14独眼蛇";
        break;
    case 1030067:
        return "Mini14钢铁阵线";
        break;

    case 103010:
        return "QBU";
        break;
    case 1030101:
        return "QBU破损";
        break;
    case 1030102:
        return "QBU修复";
        break;
    case 1030103:
        return "QBU完好";
        break;
    case 1030104:
        return "QBU改进";
        break;
    case 1030105:
        return "QBU精致";
        break;
    case 1030106:
        return "QBU独眼蛇";
        break;
    case 1030107:
        return "QBU钢铁阵线";
        break;

    case 103004:
        return "SKS";
        break;
    case 1030041:
        return "SKS破损";
        break;
    case 1030042:
        return "SKS修复";
        break;
    case 1030043:
        return "SKS完好";
        break;
    case 1030044:
        return "SKS改进";
        break;
    case 1030045:
        return "SKS精致";
        break;
    case 1030046:
        return "SKS独眼蛇";
        break;
    case 1030047:
        return "SKS钢铁阵线";
        break;

    case 103007:
        return "MK14";
        break;
    case 1030071:
        return "MK14破损";
        break;
    case 1030072:
        return "MK14修复";
        break;
    case 1030073:
        return "MK14完好";
        break;
    case 1030074:
        return "MK14改进";
        break;
    case 1030075:
        return "MK14精致";
        break;
    case 1030076:
        return "MK14独眼蛇";
        break;
    case 1030077:
        return "MK14钢铁阵线";
        break;

    case 103014:
        return "MK20-H";
        break;
    case 1030141:
        return "MK20-H破损";
        break;
    case 1030142:
        return "MK20-H修复";
        break;
    case 1030143:
        return "MK20-H完好";
        break;
    case 1030144:
        return "MK20-H改进";
        break;
    case 1030145:
        return "MK20-H精致";
        break;
    case 1030146:
        return "MK20-H独眼蛇";
        break;
    case 1030147:
        return "MK20-H钢铁阵线";
        break;

    case 103013:
        return "M4117";
        break;
    case 1030131:
        return "M4117破损";
        break;
    case 1030132:
        return "M4117修复";
        break;
    case 1030133:
        return "M4117完好";
        break;
    case 1030134:
        return "M4117改进";
        break;
    case 1030135:
        return "M4117精致";
        break;
    case 1030136:
        return "M4117独眼蛇";
        break;
    case 1030137:
        return "M4117钢铁阵线";
        break;
    // 连狙
    case 103012:
        return "ARM";
        break;
    case 1030121:
        return "ARM破损";
        break;
    case 1030122:
        return "ARM修复";
        break;
    case 1030123:
        return "ARM完好";
        break;
    case 1030124:
        return "ARM改进";
        break;
    case 1030125:
        return "ARM精致";
        break;
    case 1030126:
        return "ARM独眼蛇";
        break;
    case 1030127:
        return "ARM钢铁阵线";
        break;

    case 103003:
        return "AWM";
        break;
    case 1030031:
        return "AWM破损";
        break;
    case 1030032:
        return "AWM修复";
        break;
    case 1030033:
        return "AWM完好";
        break;
    case 1030034:
        return "AWM改进";
        break;
    case 1030035:
        return "AWM精致";
        break;
    case 1030036:
        return "AWM独眼蛇";
        break;
    case 1030037:
        return "AWM钢铁阵线";
        break;

    case 103002:
        return "M24";
        break;
    case 1030021:
        return "M24破损";
        break;
    case 1030022:
        return "M24修复";
        break;
    case 1030023:
        return "M24完好";
        break;
    case 1030024:
        return "M24改进";
        break;
    case 1030025:
        return "M24精致";
        break;
    case 1030026:
        return "M24独眼蛇";
        break;
    case 1030027:
        return "M24钢铁阵线";
        break;

    case 103011:
        return "莫幸纳甘";
        break;
    case 1030111:
        return "莫幸纳甘破损";
        break;
    case 1030112:
        return "莫幸纳甘修复";
        break;
    case 1030113:
        return "莫幸纳甘完好";
        break;
    case 1030114:
        return "莫幸纳甘改进";
        break;
    case 1030115:
        return "莫幸纳甘精致";
        break;
    case 1030116:
        return "莫幸纳甘独眼蛇";
        break;
    case 1030117:
        return "莫幸纳甘钢铁阵线";
        break;

    case 103001:
        return "Kar98K";
        break;
    case 1030011:
        return "Kar98K破损";
        break;
    case 1030012:
        return "Kar98K修复";
        break;
    case 1030013:
        return "Kar98K完好";
        break;
    case 1030014:
        return "Kar98K改进";
        break;
    case 1030015:
        return "Kar98K精致";
        break;
    case 1030016:
        return "Kar98K独眼蛇";
        break;
    case 1030017:
        return "Kar98K钢铁阵线";
        break;

    case 103008:
        return "Win94";
        break;
    case 1030081:
        return "Win94破损";
        break;
    case 1030082:
        return "Win94修复";
        break;
    case 1030083:
        return "Win94完好";
        break;
    case 1030084:
        return "Win94改进";
        break;
    case 1030085:
        return "Win94精致";
        break;
    case 1030086:
        return "Win94独眼蛇";
        break;
    case 1030087:
        return "Win94钢铁阵线";
        break;
    // 机关枪
    case 105001:
        return "M249";
        break;
    case 1050011:
        return "M249破损";
        break;
    case 1050012:
        return "M249修复";
        break;
    case 1050013:
        return "M249完好";
        break;
    case 1050014:
        return "M249改进";
        break;
    case 1050015:
        return "M249精致";
        break;
    case 1050016:
        return "M249独眼蛇";
        break;
    case 1050017:
        return "M249钢铁阵线";
        break;

    case 105002:
        return "DP-28";
        break;
    case 1050021:
        return "DP-28破损";
        break;
    case 1050022:
        return "DP-28修复";
        break;
    case 1050023:
        return "DP-28完好";
        break;
    case 1050024:
        return "DP-28改进";
        break;
    case 1050025:
        return "DP-28精致";
        break;
    case 1050026:
        return "DP-28独眼蛇";
        break;
    case 1050027:
        return "DP-28钢铁阵线";
        break;

    case 105010:
        return "MG3";
        break;
    case 1050101:
        return "MG3破损";
        break;
    case 1050102:
        return "MG3修复";
        break;
    case 1050103:
        return "MG3完好";
        break;
    case 1050104:
        return "MG3改进";
        break;
    case 1050105:
        return "MG3精致";
        break;
    case 1050106:
        return "MG3独眼蛇";
        break;
    case 1050107:
        return "MG3钢铁阵线";
        break;

    case 107001:
        return "十字弩";
        break;
    case 1070011:
        return "十字弩破损";
        break;
    case 1070012:
        return "十字弩修复";
        break;
    case 1070013:
        return "十字弩完好";
        break;
    case 1070014:
        return "十字弩改进";
        break;
    case 1070015:
        return "十字弩精致";
        break;
    case 1070016:
        return "十字弩独眼蛇";
        break;
    case 1070017:
        return "十字弩钢铁阵线";
        break;

    case 107007:
        return "爆炸猎弓";
        break;
    case 1070071:
        return "爆炸猎弓破损";
        break;
    case 1070072:
        return "爆炸猎弓修复";
        break;
    case 1070073:
        return "爆炸猎弓完好";
        break;
    case 1070074:
        return "爆炸猎弓改进";
        break;
    case 1070075:
        return "爆炸猎弓精致";
        break;
    case 1070076:
        return "爆炸猎弓独眼蛇";
        break;
    case 1070077:
        return "爆炸猎弓钢铁阵线";
        break;
    // 冲锋枪
    case 102001:
        return "UZI";
        break;
    case 1020011:
        return "UZI破损";
        break;
    case 1020012:
        return "UZI修复";
        break;
    case 1020013:
        return "UZI完好";
        break;
    case 1020014:
        return "UZI改进";
        break;
    case 1020015:
        return "UZI精致";
        break;
    case 1020016:
        return "UZI独眼蛇";
        break;
    case 1020017:
        return "UZI钢铁阵线";
        break;

    case 102003:
        return "Vector";
        break;
    case 1020031:
        return "Vector破损";
        break;
    case 1020032:
        return "Vector修复";
        break;
    case 1020033:
        return "Vector完好";
        break;
    case 1020034:
        return "Vector改进";
        break;
    case 1020035:
        return "Vector精致";
        break;
    case 1020036:
        return "Vector独眼蛇";
        break;
    case 1020037:
        return "Vector钢铁阵线";
        break;

    case 100103:
        return "PP-19";
        break;
    case 1001031:
        return "PP-19破损";
        break;
    case 1001032:
        return "PP-19修复";
        break;
    case 1001033:
        return "PP-19完好";
        break;
    case 1001034:
        return "PP-19改进";
        break;
    case 1001035:
        return "PP-19精致";
        break;
    case 1001036:
        return "PP-19独眼蛇";
        break;
    case 1001037:
        return "PP-19钢铁阵线";
        break;

    case 102007:
        return "MP5K";
        break;
    case 1020071:
        return "MP5K破损";
        break;
    case 1020072:
        return "MP5K修复";
        break;
    case 1020073:
        return "MP5K完好";
        break;
    case 1020074:
        return "MP5K改进";
        break;
    case 1020075:
        return "MP5K精致";
        break;
    case 1020076:
        return "MP5K独眼蛇";
        break;
    case 1020077:
        return "MP5K钢铁阵线";
        break;

    case 102002:
        return "UMP-45";
        break;
    case 1020021:
        return "UMP-45破损";
        break;
    case 1020022:
        return "UMP-45修复";
        break;
    case 1020023:
        return "UMP-45完好";
        break;
    case 1020024:
        return "UMP-45改进";
        break;
    case 1020025:
        return "UMP-45精致";
        break;
    case 1020026:
        return "UMP-45独眼蛇";
        break;
    case 1020027:
        return "UMP-45钢铁阵线";
        break;

    case 102004:
        return "汤姆逊";
        break;
    case 1020041:
        return "汤姆逊破损";
        break;
    case 1020042:
        return "汤姆逊修复";
        break;
    case 1020043:
        return "汤姆逊完好";
        break;
    case 1020044:
        return "汤姆逊改进";
        break;
    case 1020045:
        return "汤姆逊精致";
        break;
    case 1020046:
        return "汤姆逊独眼蛇";
        break;
    case 1020047:
        return "汤姆逊钢铁阵线";
        break;

    case 102105:
        return "P90";
        break;
    case 1021051:
        return "P90破损";
        break;
    case 1021052:
        return "P90修复";
        break;
    case 1021053:
        return "P90完好";
        break;
    case 1021054:
        return "P90改进";
        break;
    case 1021055:
        return "P90精致";
        break;
    case 1021056:
        return "P90独眼蛇";
        break;
    case 1021057:
        return "P90钢铁阵线";
        break;

    case 102005:
        return "野牛";
        break;
    case 1020051:
        return "野牛破损";
        break;
    case 1020052:
        return "野牛修复";
        break;
    case 1020053:
        return "野牛完好";
        break;
    case 1020054:
        return "野牛改进";
        break;
    case 1020055:
        return "野牛精致";
        break;
    case 1020056:
        return "野牛独眼蛇";
        break;
    case 1020057:
        return "野牛钢铁阵线";
        break;
    // 霰弹枪
    case 104001:
        return "S686";
        break;
    case 1040011:
        return "S686破损";
        break;
    case 1040012:
        return "S686修复";
        break;
    case 1040013:
        return "S686完好";
        break;
    case 1040014:
        return "S686改进";
        break;
    case 1040015:
        return "S686精致";
        break;
    case 1040016:
        return "S686独眼蛇";
        break;
    case 1040017:
        return "S686钢铁阵线";
        break;

    case 104002:
        return "S1897";
        break;
    case 1040021:
        return "S1897破损";
        break;
    case 1040022:
        return "S1897修复";
        break;
    case 1040023:
        return "S1897完好";
        break;
    case 1040024:
        return "S1897改进";
        break;
    case 1040025:
        return "S1897精致";
        break;
    case 1040026:
        return "S1897独眼蛇";
        break;
    case 1040027:
        return "S1897钢铁阵线";
        break;

    case 104003:
        return "S12K";
        break;
    case 1040031:
        return "S12K破损";
        break;
    case 1040032:
        return "S12K修复";
        break;
    case 1040033:
        return "S12K完好";
        break;
    case 1040034:
        return "S12K改进";
        break;
    case 1040035:
        return "S12K精致";
        break;
    case 1040036:
        return "S12K独眼蛇";
        break;
    case 1040037:
        return "S12K钢铁阵线";
        break;

    case 104004:
        return "DBS";
        break;
    case 1040041:
        return "DBS破损";
        break;
    case 1040042:
        return "DBS修复";
        break;
    case 1040043:
        return "DBS完好";
        break;
    case 1040044:
        return "DBS改进";
        break;
    case 1040045:
        return "DBS精致";
        break;
    case 1040046:
        return "DBS独眼蛇";
        break;
    case 1040047:
        return "DBS钢铁阵线";
        break;

    case 104100:
        return "SPAS-12";
        break;
    case 1041001:
        return "SPAS-12破损";
        break;
    case 1041002:
        return "SPAS-12修复";
        break;
    case 1041003:
        return "SPAS-12完好";
        break;
    case 1041004:
        return "SPAS-12改进";
        break;
    case 1041005:
        return "SPAS-12精致";
        break;
    case 1041006:
        return "SPAS-12独眼蛇";
        break;
    case 1041007:
        return "SPAS-12钢铁阵线";
        break;
    // 投掷爆炸物
    case 602004:
        return "手榴弹";
        break;
    case 602003:
        return "燃烧瓶";
        break;
    case 602002:
        return "烟雾弹";
        break;
    case 602001:
        return "震撼弹";
        break;
    // 近战武器
    case 108003:
        return "镰刀";
        break;
    case 108002:
        return "撬棍";
        break;
    case 108001:
        return "大砍刀";
        break;
    case 108004:
        return "平底锅";
        break;
    case 0:
        return "拳头";
        break;
    default:
        return "未收录";
        break;
    }
    return nullptr;
}
